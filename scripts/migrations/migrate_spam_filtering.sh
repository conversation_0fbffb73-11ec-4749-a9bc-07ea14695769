#!/bin/bash

# Spam Filtering Migration Script
# This script migrates an existing installation to support spam filtering
# 
# What this script does:
# 1. Installs SpamAssassin and Amavis
# 2. Configures SpamAssassin and Amavis services
# 3. Updates SQLite database schema
# 4. Updates Postfix configuration for transport maps
# 5. Restarts services
#
# Usage: sudo ./scripts/migrations/migrate_spam_filtering.sh

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SQLITE_DB_PATH="/opt/eu-email-webhook/data/postfix.db"
TRANSPORT_CF_PATH="/opt/eu-email-webhook/data/transport.cf"
POSTFIX_MAIN_CF="/etc/postfix/main.cf"
POSTFIX_MASTER_CF="/etc/postfix/master.cf"

echo -e "${BLUE}🚀 Starting Spam Filtering Migration${NC}"
echo "This script will install and configure SpamAssassin + Amavis for spam filtering"
echo

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}❌ This script must be run as root${NC}"
   echo "Usage: sudo ./scripts/migrations/migrate_spam_filtering.sh"
   exit 1
fi

# Backup existing configuration
echo -e "${YELLOW}📦 Creating configuration backups...${NC}"
cp "$POSTFIX_MAIN_CF" "$POSTFIX_MAIN_CF.backup.$(date +%Y%m%d_%H%M%S)"
cp "$POSTFIX_MASTER_CF" "$POSTFIX_MASTER_CF.backup.$(date +%Y%m%d_%H%M%S)"
if [ -f "$SQLITE_DB_PATH" ]; then
    cp "$SQLITE_DB_PATH" "$SQLITE_DB_PATH.backup.$(date +%Y%m%d_%H%M%S)"
fi
echo -e "${GREEN}✅ Backups created${NC}"

# Step 1: Install SpamAssassin and Amavis
echo -e "${YELLOW}📦 Installing SpamAssassin and Amavis...${NC}"
apt-get update
apt-get install -y spamassassin amavisd-new

# Step 2: Configure SpamAssassin
echo -e "${YELLOW}⚙️  Configuring SpamAssassin...${NC}"
systemctl enable spamd
systemctl start spamd

# Update SpamAssassin configuration
cat > /etc/default/spamd << 'EOF'
# /etc/default/spamd
# Duncan Findlay

# WARNING: please read README.spamd before using.
# There may be security implications.

# Change to one to enable spamd
ENABLED=1

# Options
# See man spamd for possible options. The -d option is automatically added.

# SpamAssassin uses a preforking model, so be careful! You need to
# make sure --max-children is not set to anything higher than 5,
# unless you know what you're doing.

SPAMD_HOME="/var/lib/spamassassin/"
OPTIONS="--create-prefs --max-children 5 --helper-home-dir"

# Pid file
# Where should spamd write its PID to file? If you use the -u or
# --username option above, this needs to be writable by that user.
# Otherwise, the init script will not be able to shut spamd down.
PIDFILE="/var/run/spamd.pid"

# Set nice level of spamd
#NICE="--nicelevel 15"

# Cronjob
# Set to anything but 0 to enable the cron job to automatically update
# spamassassin's rules on a nightly basis
CRON=1
EOF

# Step 3: Configure Amavis
echo -e "${YELLOW}⚙️  Configuring Amavis...${NC}"

# Configure Amavis FQDN
cat > /etc/amavis/conf.d/05-node_id << 'EOF'
use strict;

# Set the fully qualified domain name for email service
$myhostname = 'mx1.emailconnect.eu';

1;  # ensure a defined return
EOF

# Enable Amavis
systemctl enable amavis
systemctl start amavis

# Configure Amavis for SpamAssassin integration
cat > /etc/amavis/conf.d/15-content_filter_mode << 'EOF'
# Enable spam checking
@bypass_spam_checks_maps = (
   \%bypass_spam_checks, \@bypass_spam_checks_acl, \$bypass_spam_checks_re);

# Enable SpamAssassin
$sa_tag_level_deflt  = 2.0;  # add spam info headers if at, or above that level
$sa_tag2_level_deflt = 6.2;  # add 'spam detected' headers at that level
$sa_kill_level_deflt = 6.9;  # triggers spam evasive actions (e.g. blocks mail)
$sa_dsn_cutoff_level = 10;   # spam level beyond which a DSN is not sent
$sa_crediblefrom_dsn_cutoff_level = 18; # likewise, but for a likely valid From

# Enable anti-spam checking
$bypass_spam_checks_maps = [0];
EOF

# Configure Amavis ports
cat > /etc/amavis/conf.d/50-user << 'EOF'
# Local configuration for Amavis

# Listen on port 10024 for mail from Postfix
$inet_socket_port = 10024;

# Send mail back to Postfix on port 10025
$notify_method  = 'smtp:[127.0.0.1]:10025';
$forward_method = 'smtp:[127.0.0.1]:10025';

# Domain-specific settings can be added here
EOF

echo -e "${GREEN}✅ SpamAssassin and Amavis installed and configured${NC}"

# Step 4: Update SQLite database schema
echo -e "${YELLOW}🗄️  Updating database schema...${NC}"
if [ -f "$SQLITE_DB_PATH" ]; then
    sqlite3 "$SQLITE_DB_PATH" < scripts/migrations/001_add_spam_filtering.sql
    echo -e "${GREEN}✅ Database schema updated${NC}"
else
    echo -e "${RED}❌ SQLite database not found at $SQLITE_DB_PATH${NC}"
    exit 1
fi

# Step 5: Update Postfix configuration files for spam filtering
echo -e "${YELLOW}⚙️  Updating Postfix configuration files...${NC}"

# Create SQL view for spam filtering integration
echo -e "${YELLOW}🛡️  Creating spam filtering view...${NC}"
sqlite3 /opt/eu-email-webhook/data/postfix.db << 'EOSQL'
CREATE VIEW IF NOT EXISTS virtual_aliases_with_spam AS
SELECT 
    va.email,
    CASE 
        WHEN vd.spam_filtering = 1 THEN 'amavis:[127.0.0.1]:10024'
        ELSE va.destination 
    END as destination,
    va.domain,
    va.active
FROM virtual_aliases va 
JOIN virtual_domains vd ON va.domain = vd.domain 
WHERE va.active = 1 AND vd.active = 1;
EOSQL

# Update virtual_domains.cf to use dynamic routing based on spam_filtering
cat > /opt/eu-email-webhook/data/virtual_domains.cf << 'EOF'
dbpath = /opt/eu-email-webhook/data/postfix.db
query = SELECT CASE WHEN spam_filtering = 1 THEN '<EMAIL>' ELSE '<EMAIL>' END FROM virtual_domains WHERE domain='%s' AND active=1
EOF

# Update virtual_aliases.cf to use dynamic routing based on spam_filtering
cat > /opt/eu-email-webhook/data/virtual_aliases.cf << 'EOF'
dbpath = /opt/eu-email-webhook/data/postfix.db
query = SELECT CASE WHEN vd.spam_filtering = 1 THEN '<EMAIL>' ELSE '<EMAIL>' END FROM virtual_aliases va JOIN virtual_domains vd ON va.domain = vd.domain WHERE va.email='%s' AND va.active=1 AND vd.active=1
EOF

# Create transport.cf placeholder (not used in alias-based spam filtering)
cat > "$TRANSPORT_CF_PATH" << 'EOF'
# ⚠️  OBSOLETE FILE - NOT USED IN ALIAS-BASED SPAM FILTERING ⚠️
# This file is created for compatibility but is not used by Postfix
# The new alias-based approach uses virtual_domains.cf and virtual_aliases.cf
# with dynamic SQL queries based on the spam_filtering column
#
# OLD QUERY (NOT USED):
# query = SELECT CASE WHEN spam_filtering = 1 THEN 'amavis:[127.0.0.1]:10024' ELSE NULL END FROM virtual_domains WHERE domain='%s' AND active=1
# dbpath = /opt/eu-email-webhook/data/postfix.db
#
# NEW APPROACH: See virtual_domains.cf and virtual_aliases.cf for actual routing logic
EOF

# Set proper permissions
chown postfix:postfix /opt/eu-email-webhook/data/virtual_domains.cf
chown postfix:postfix /opt/eu-email-webhook/data/virtual_aliases.cf
chown postfix:postfix "$TRANSPORT_CF_PATH"
chmod 644 /opt/eu-email-webhook/data/virtual_domains.cf
chmod 644 /opt/eu-email-webhook/data/virtual_aliases.cf
chmod 644 "$TRANSPORT_CF_PATH"
echo -e "${GREEN}✅ Postfix configuration files updated${NC}"

# Step 6: Update Postfix main.cf
echo -e "${YELLOW}⚙️  Updating Postfix configuration...${NC}"

# Remove transport_maps configuration if present (not needed for alias-based approach)
if grep -q "transport_maps.*sqlite:" "$POSTFIX_MAIN_CF"; then
    echo "Removing transport_maps from main.cf (not needed for alias-based spam filtering)..."
    sed -i '/^transport_maps.*sqlite:/d' "$POSTFIX_MAIN_CF"
    sed -i '/^# Spam filtering transport maps/d' "$POSTFIX_MAIN_CF"
fi

# Add Amavis configuration to master.cf if not already present
if ! grep -q "127.0.0.1:10025" "$POSTFIX_MASTER_CF"; then
    echo "" >> "$POSTFIX_MASTER_CF"
    echo "# Amavis integration for spam filtering" >> "$POSTFIX_MASTER_CF"
    echo "127.0.0.1:10025 inet n - - - - smtpd" >> "$POSTFIX_MASTER_CF"
    echo "    -o content_filter=" >> "$POSTFIX_MASTER_CF"
    echo "    -o local_recipient_maps=" >> "$POSTFIX_MASTER_CF"
    echo "    -o relay_recipient_maps=" >> "$POSTFIX_MASTER_CF"
    echo "    -o smtpd_restriction_classes=" >> "$POSTFIX_MASTER_CF"
    echo "    -o smtpd_delay_reject=no" >> "$POSTFIX_MASTER_CF"
    echo "    -o smtpd_client_restrictions=permit_mynetworks,reject" >> "$POSTFIX_MASTER_CF"
    echo "    -o smtpd_helo_restrictions=" >> "$POSTFIX_MASTER_CF"
    echo "    -o smtpd_sender_restrictions=" >> "$POSTFIX_MASTER_CF"
    echo "    -o smtpd_recipient_restrictions=permit_mynetworks,reject" >> "$POSTFIX_MASTER_CF"
    echo "    -o mynetworks=*********/8" >> "$POSTFIX_MASTER_CF"
    echo "    -o smtpd_authorized_xforward_hosts=*********/8" >> "$POSTFIX_MASTER_CF"
fi

echo -e "${GREEN}✅ Postfix configuration updated${NC}"

# Step 7: Restart services
echo -e "${YELLOW}🔄 Restarting services...${NC}"
systemctl restart spamd
systemctl restart amavis
systemctl restart postfix

# Verify services are running
echo -e "${YELLOW}🔍 Verifying services...${NC}"
if systemctl is-active --quiet spamd; then
    echo -e "${GREEN}✅ SpamAssassin is running${NC}"
else
    echo -e "${RED}❌ SpamAssassin failed to start${NC}"
fi

if systemctl is-active --quiet amavis; then
    echo -e "${GREEN}✅ Amavis is running${NC}"
else
    echo -e "${RED}❌ Amavis failed to start${NC}"
fi

if systemctl is-active --quiet postfix; then
    echo -e "${GREEN}✅ Postfix is running${NC}"
else
    echo -e "${RED}❌ Postfix failed to start${NC}"
fi

echo
echo -e "${GREEN}🎉 Spam filtering migration completed successfully!${NC}"
echo
echo -e "${BLUE}Next steps:${NC}"
echo "1. Test the spam filtering functionality through the web interface"
echo "2. Configure spam filtering settings for your domains"
echo "3. Monitor logs for any issues: journalctl -f -u postfix -u amavis -u spamd"
echo
echo -e "${YELLOW}Note:${NC} Existing domains will have spam filtering disabled by default."
echo "Enable it per domain through the web interface or API."
