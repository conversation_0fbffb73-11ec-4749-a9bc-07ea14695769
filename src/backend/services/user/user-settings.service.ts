import { prisma } from '../../lib/prisma.js';
import { logger } from '../../utils/logger.js';

export interface UserSettingsData {
  maxInlineSize?: number;
  allowMediaFiles?: boolean;
  storageProvider?: string;
  s3Config?: {
    region?: string;
    bucket?: string;
    accessKey?: string;
    secretKey?: string;
    endpoint?: string;
  };
}

export interface CreateUserSettingsData extends UserSettingsData {
  userId: string;
}

export class UserSettingsService {
  /**
   * Get user settings by user ID
   */
  async getUserSettings(userId: string) {
    try {
      const settings = await prisma.userSettings.findUnique({
        where: { userId },
        select: {
          id: true,
          maxInlineSize: true,
          allowMediaFiles: true,
          storageProvider: true,
          s3Config: true,
          createdAt: true,
          updatedAt: true
        }
      });

      // Return default settings if none exist
      if (!settings) {
        return {
          maxInlineSize: 1.0,
          allowMediaFiles: false,
          storageProvider: 'default',
          s3Config: null,
          createdAt: null,
          updatedAt: null
        };
      }

      return settings;
    } catch (error: any) {
      logger.error({ error: error.message, userId }, 'Failed to get user settings');
      throw new Error('Failed to retrieve user settings');
    }
  }

  /**
   * Create or update user settings
   */
  async upsertUserSettings(userId: string, data: UserSettingsData) {
    try {
      // Validate input data
      if (data.maxInlineSize !== undefined) {
        if (typeof data.maxInlineSize !== 'number' || data.maxInlineSize < 0.1 || data.maxInlineSize > 10) {
          throw new Error('maxInlineSize must be a number between 0.1 and 10');
        }
      }

      if (data.storageProvider !== undefined) {
        if (!['default', 's3-compatible'].includes(data.storageProvider)) {
          throw new Error('storageProvider must be either "default" or "s3-compatible"');
        }
      }

      // Validate S3 config if provided
      if (data.s3Config !== undefined && data.storageProvider === 's3-compatible') {
        const { region, bucket, accessKey, secretKey, endpoint } = data.s3Config;
        
        if (!region || !bucket || !accessKey || !secretKey) {
          throw new Error('S3 configuration requires region, bucket, accessKey, and secretKey');
        }

        // Basic validation for S3 fields
        if (typeof region !== 'string' || region.length < 2) {
          throw new Error('S3 region must be a valid string');
        }
        if (typeof bucket !== 'string' || bucket.length < 3) {
          throw new Error('S3 bucket name must be at least 3 characters');
        }
        if (typeof accessKey !== 'string' || accessKey.length < 10) {
          throw new Error('S3 access key must be at least 10 characters');
        }
        if (typeof secretKey !== 'string' || secretKey.length < 10) {
          throw new Error('S3 secret key must be at least 10 characters');
        }
        if (endpoint && (typeof endpoint !== 'string' || !endpoint.startsWith('http'))) {
          throw new Error('S3 endpoint must be a valid URL');
        }
      }

      // Prepare update data
      const updateData: any = {};
      if (data.maxInlineSize !== undefined) updateData.maxInlineSize = data.maxInlineSize;
      if (data.allowMediaFiles !== undefined) updateData.allowMediaFiles = data.allowMediaFiles;
      if (data.storageProvider !== undefined) updateData.storageProvider = data.storageProvider;
      if (data.s3Config !== undefined) updateData.s3Config = data.s3Config;

      const settings = await prisma.userSettings.upsert({
        where: { userId },
        update: updateData,
        create: {
          userId,
          maxInlineSize: data.maxInlineSize ?? 1.0,
          allowMediaFiles: data.allowMediaFiles ?? false,
          storageProvider: data.storageProvider ?? 'default',
          s3Config: data.s3Config ?? null
        },
        select: {
          id: true,
          maxInlineSize: true,
          allowMediaFiles: true,
          storageProvider: true,
          s3Config: true,
          createdAt: true,
          updatedAt: true
        }
      });

      logger.info({ userId, settingsId: settings.id }, 'User settings updated successfully');
      return settings;
    } catch (error: any) {
      logger.error({ error: error.message, userId }, 'Failed to update user settings');
      throw error;
    }
  }

  /**
   * Delete user settings (reset to defaults)
   */
  async deleteUserSettings(userId: string) {
    try {
      const deleted = await prisma.userSettings.delete({
        where: { userId }
      });

      logger.info({ userId, settingsId: deleted.id }, 'User settings deleted successfully');
      return { success: true, message: 'Settings reset to defaults' };
    } catch (error: any) {
      if (error.code === 'P2025') {
        // Record not found - this is fine, settings already don't exist
        return { success: true, message: 'Settings already at defaults' };
      }
      
      logger.error({ error: error.message, userId }, 'Failed to delete user settings');
      throw new Error('Failed to reset user settings');
    }
  }

  /**
   * Check if user has Pro plan for advanced settings
   */
  async validateProFeatures(userId: string, data: UserSettingsData): Promise<{ allowed: boolean; reason?: string }> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { planType: true }
      });

      if (!user) {
        return { allowed: false, reason: 'User not found' };
      }

      const isProUser = user.planType === 'pro' || user.planType === 'enterprise';

      // Check Pro-only features
      if (!isProUser) {
        if (data.maxInlineSize && data.maxInlineSize > 1.0) {
          return { allowed: false, reason: 'Attachment size above 1MB requires Pro plan' };
        }
        if (data.allowMediaFiles === true) {
          return { allowed: false, reason: 'Media file processing requires Pro plan' };
        }
        if (data.storageProvider === 's3-compatible') {
          return { allowed: false, reason: 'Custom storage providers require Pro plan' };
        }
      }

      return { allowed: true };
    } catch (error: any) {
      logger.error({ error: error.message, userId }, 'Failed to validate Pro features');
      return { allowed: false, reason: 'Failed to validate plan permissions' };
    }
  }
}
