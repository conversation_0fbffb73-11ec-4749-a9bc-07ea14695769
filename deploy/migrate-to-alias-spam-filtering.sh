#!/bin/bash

# Migration script: Amavis-based spam filtering → Alias-based spam filtering
# This script migrates existing installations from the old Amavis approach to the new alias-based approach
# 
# IMPORTANT: This script should be run on production servers that are currently using Amavis
# for spam filtering and need to migrate to the new alias-based approach.

set -e

echo "🔄 EU Email Webhook: Migrating to Alias-Based Spam Filtering"
echo "=============================================================="
echo ""
echo "This script will:"
echo "  1. Stop Amavis service"
echo "  2. Update Postfix configuration to remove Amavis integration"
echo "  3. Add spam-process-email alias"
echo "  4. Update virtual_aliases.cf to use direct table queries"
echo "  5. Update all existing domains to use plan-based routing"
echo "  6. Remove Amavis package (optional)"
echo ""

# Check if running as root or with sudo
if [[ $EUID -eq 0 ]]; then
    echo "⚠️  Running as root. This is fine for system configuration."
elif sudo -n true 2>/dev/null; then
    echo "✅ Running with sudo access."
else
    echo "❌ This script requires sudo access. Please run with sudo or as root."
    exit 1
fi

# Confirm migration
read -p "⚠️  This will modify your production email configuration. Continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Migration cancelled."
    exit 1
fi

echo ""
echo "🛑 Step 1: Stopping Amavis service..."
sudo systemctl stop amavis || echo "⚠️  Amavis service was not running or doesn't exist"
sudo systemctl disable amavis || echo "⚠️  Amavis service was not enabled or doesn't exist"

echo ""
echo "📝 Step 2: Backing up current Postfix configuration..."
sudo cp /etc/postfix/main.cf /etc/postfix/main.cf.backup.$(date +%Y%m%d_%H%M%S)
sudo cp /etc/postfix/master.cf /etc/postfix/master.cf.backup.$(date +%Y%m%d_%H%M%S)

echo ""
echo "🔧 Step 3: Removing Amavis integration from Postfix..."

# Remove transport_maps from main.cf if it exists
if grep -q "transport_maps" /etc/postfix/main.cf; then
    echo "Removing transport_maps from main.cf..."
    sudo sed -i '/^transport_maps/d' /etc/postfix/main.cf
fi

# Remove Amavis listener from master.cf
if grep -q "127.0.0.1:10025" /etc/postfix/master.cf; then
    echo "Removing Amavis listener from master.cf..."
    # Remove the Amavis section (from the inet line to the end of its configuration)
    sudo sed -i '/^127\.0\.0\.1:10025 inet/,/^[[:space:]]*-o smtpd_authorized_xforward_hosts=/d' /etc/postfix/master.cf
fi

echo ""
echo "📧 Step 4: Adding spam-process-email alias..."
if ! grep -q "spam-process-email:" /etc/aliases; then
    echo "spam-process-email: |/opt/eu-email-webhook/scripts/production/spam-process-email.js" | sudo tee -a /etc/aliases
    sudo newaliases
    echo "✅ spam-process-email alias added"
else
    echo "✅ spam-process-email alias already exists"
fi

echo ""
echo "🗃️  Step 5: Updating virtual_aliases.cf configuration..."
sudo tee /opt/eu-email-webhook/data/virtual_aliases.cf > /dev/null <<'EOF'
dbpath = /opt/eu-email-webhook/data/postfix.db
query = SELECT destination FROM virtual_aliases WHERE email='%s' AND active=1
EOF

echo "✅ virtual_aliases.cf updated to use direct table queries"

echo ""
echo "🔄 Step 6: Migrating existing domain routing..."

# Check if SQLite database exists
if [ ! -f /opt/eu-email-webhook/data/postfix.db ]; then
    echo "❌ SQLite database not found at /opt/eu-email-webhook/data/postfix.db"
    echo "Please ensure the application is properly installed."
    exit 1
fi

# Get all domains and their current spam filtering settings
echo "Analyzing existing domains..."
DOMAINS=$(sqlite3 /opt/eu-email-webhook/data/postfix.db "SELECT domain FROM virtual_domains WHERE active=1;")

if [ -z "$DOMAINS" ]; then
    echo "⚠️  No active domains found in database"
else
    echo "Found domains: $DOMAINS"
    
    # For each domain, we need to update the virtual_aliases to use plan-based routing
    # Since we don't have direct access to user plan information in this script,
    # we'll set all domains to use 'process-email' (free tier) by default
    # The main application will update these when users' plans are synced
    
    echo "Updating virtual aliases to use process-email (will be updated by app based on user plans)..."
    sqlite3 /opt/eu-email-webhook/data/postfix.db << 'EOSQL'
UPDATE virtual_aliases 
SET destination = 'process-email' 
WHERE destination LIKE '%amavis%' OR destination LIKE '%127.0.0.1:10024%';
EOSQL
    
    echo "✅ Virtual aliases updated"
fi

echo ""
echo "🔄 Step 7: Removing old transport configuration..."
if [ -f /opt/eu-email-webhook/data/transport.cf ]; then
    sudo rm /opt/eu-email-webhook/data/transport.cf
    echo "✅ transport.cf removed"
fi

# Remove transport database if it exists
if [ -f /opt/eu-email-webhook/data/transport.db ]; then
    sudo rm /opt/eu-email-webhook/data/transport.db
    echo "✅ transport.db removed"
fi

echo ""
echo "🔄 Step 8: Reloading Postfix configuration..."
sudo systemctl reload postfix
echo "✅ Postfix configuration reloaded"

echo ""
echo "🧹 Step 9: Cleanup (optional)..."
read -p "Remove Amavis package completely? This will free up disk space but make rollback harder. (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Removing Amavis package..."
    sudo apt remove --purge amavisd-new -y
    sudo apt autoremove -y
    echo "✅ Amavis package removed"
else
    echo "✅ Amavis package kept (disabled)"
fi

echo ""
echo "🎉 Migration completed successfully!"
echo ""
echo "📋 Summary of changes:"
echo "  ✅ Amavis service stopped and disabled"
echo "  ✅ Postfix configuration updated (transport_maps removed)"
echo "  ✅ Amavis listener removed from master.cf"
echo "  ✅ spam-process-email alias added"
echo "  ✅ virtual_aliases.cf updated for direct queries"
echo "  ✅ Existing aliases updated to use process-email"
echo "  ✅ Old transport configuration removed"
echo ""
echo "⚠️  Important notes:"
echo "  - All domains are now set to use 'process-email' (free tier routing)"
echo "  - The main application will automatically update routing based on user plans"
echo "  - Pro+ users will be automatically routed to spam-process-email"
echo "  - Monitor logs to ensure email processing continues normally"
echo ""
echo "📝 Verification commands:"
echo "  Check Postfix status: sudo systemctl status postfix"
echo "  Check SpamAssassin: sudo systemctl status spamd"
echo "  Check aliases: cat /etc/aliases | grep spam-process-email"
echo "  Check virtual aliases: sqlite3 /opt/eu-email-webhook/data/postfix.db \"SELECT * FROM virtual_aliases LIMIT 5;\""
echo ""
echo "🔍 Monitor logs:"
echo "  Postfix: journalctl -u postfix.service -f"
echo "  SpamAssassin: journalctl -u spamd.service -f"
echo "  Application: docker compose -f /opt/eu-email-webhook/docker-compose.prod.yml logs -f"
