#!/bin/bash

# Server deployment script for EU Email Webhook Service
# Updated for /opt/eu-email-webhook standardization and Docker-based deployment

set -e

APP_DIR="/opt/eu-email-webhook"
REPO_URL="**************:xadi-hq/eu-email-webhook.git"
SYSTEM_USER="www-data"  # Standard web server user

echo "🚀 Deploying EU Email Webhook Service to production server"
echo "Server: $(hostname -I | awk '{print $1}')"
echo "User: $(whoami)"
echo ""

# 1. Create application directory and set permissions
echo "📂 Setting up application directory..."
sudo mkdir -p $APP_DIR
sudo chown -R $SYSTEM_USER:$SYSTEM_USER $APP_DIR

# Clone repository if not exists
if [ ! -d "$APP_DIR/.git" ]; then
    echo "📂 Cloning repository..."
    sudo -u $SYSTEM_USER git clone $REPO_URL $APP_DIR
else
    echo "📂 Updating repository..."
    cd $APP_DIR
    sudo -u $SYSTEM_USER git pull origin main
fi

cd $APP_DIR
echo "✅ Repository updated"
echo ""

# 2. Install system dependencies
echo "📦 Installing system dependencies..."
sudo apt update
sudo apt install -y \
    postfix \
    postfix-sqlite \
    spamassassin \
    docker.io \
    curl \
    jq \
    nginx \
    certbot \
    python3-certbot-nginx \
    sqlite3

echo "✅ System dependencies installed"
echo ""

# 3. Configure Docker permissions
echo "🐳 Setting up Docker..."
sudo usermod -aG docker $SYSTEM_USER
sudo systemctl enable docker
sudo systemctl start docker

echo "✅ Docker configured"
echo ""

# 4. Configure spam filtering (SpamAssassin only)
echo "🛡️  Configuring spam filtering..."

# Configure SpamAssassin
echo "Configuring SpamAssassin..."
sudo tee /etc/default/spamd > /dev/null <<'EOF'
# SpamAssassin configuration for EU Email Webhook
ENABLED=1
SPAMD_HOME="/var/lib/spamassassin/"
OPTIONS="--create-prefs --max-children 5 --helper-home-dir"
PIDFILE="/var/run/spamd.pid"
CRON=1
EOF

sudo systemctl enable spamd
sudo systemctl start spamd

# Add spam-process-email alias to /etc/aliases
echo "Adding spam-process-email alias..."
if ! grep -q "spam-process-email:" /etc/aliases; then
    echo "spam-process-email: |/usr/bin/node /opt/eu-email-webhook/scripts/production/spam-process-email.js" | sudo tee -a /etc/aliases
    sudo newaliases
fi

echo "✅ Spam filtering configured"
echo ""

# 5. Set up environment configuration
if [ ! -f "$APP_DIR/.env.prod" ]; then
    echo "⚙️  Creating production environment configuration..."
    sudo -u $SYSTEM_USER cp $APP_DIR/.env.prod.example $APP_DIR/.env.prod

    # Generate secure JWT secret
    JWT_SECRET=$(openssl rand -base64 32)
    sudo -u $SYSTEM_USER sed -i "s|your-super-secret-jwt-key|$JWT_SECRET|g" $APP_DIR/.env.prod

    echo "✅ Environment configuration created"
    echo "📝 Please review and update $APP_DIR/.env.prod with your specific values"
else
    echo "⚙️  Environment configuration already exists"
fi

# 6. Create data directories with proper permissions
echo "📁 Creating data directories..."
sudo mkdir -p /opt/eu-email-webhook/data
sudo mkdir -p /opt/eu-email-webhook/scripts
sudo mkdir -p /opt/eu-email-webhook/backups
sudo chown -R $SYSTEM_USER:$SYSTEM_USER /opt/eu-email-webhook

echo "✅ Data directories created"
echo ""

# 7. Build and start Docker containers
echo "🐳 Building and starting Docker containers..."
cd $APP_DIR
sudo -u $SYSTEM_USER docker compose -f docker-compose.prod.yml build
sudo -u $SYSTEM_USER docker compose -f docker-compose.prod.yml up -d

# Initialize scripts volume
sudo -u $SYSTEM_USER docker compose -f docker-compose.prod.yml --profile init up script-init

echo "✅ Docker containers started"
echo ""

# 7. Configure firewall
echo "🔥 Configuring firewall..."
sudo ufw allow 22    # SSH
sudo ufw allow 25    # SMTP
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
# Note: Internal API ports (3000, 3001) stay internal

echo "✅ Firewall configured"
echo ""

# 8. Start spam filtering services
echo "🛡️  Starting spam filtering services..."
sudo systemctl start spamd

echo "✅ Spam filtering services started"
echo ""

# 9. Set up Nginx reverse proxy
echo "🌐 Setting up Nginx..."
sudo tee /etc/nginx/sites-available/eu-email-webhook > /dev/null <<EOF
server {
    listen 80;
    server_name $(hostname -I | awk '{print $1}') emailconnect.eu;
    
    # WebSocket proxy for Socket.IO
    location /socket.io/ {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 86400;
    }
    
    # API proxy
    location /api/ {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_read_timeout 30s;
        proxy_connect_timeout 10s;
    }
    
    # Health checks
    location /health/postfix {
        proxy_pass http://127.0.0.1:3001/health;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        access_log off;
    }

    location /health {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        access_log off;
    }

    # Proxy all other requests to the Fastify app
    location / {
        proxy_pass http://127.0.0.1:3000;
        add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

# Enable the site
sudo ln -sf /etc/nginx/sites-available/eu-email-webhook /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx

echo "✅ Nginx configured"
echo ""

# 10. Set up Postfix for SQLite and spam filtering
echo "📧 Configuring Postfix for SQLite support and spam filtering..."
sudo postconf -e "virtual_alias_domains = sqlite:/opt/eu-email-webhook/data/virtual_domains.cf"
sudo postconf -e "virtual_alias_maps = sqlite:/opt/eu-email-webhook/data/virtual_aliases.cf"
sudo postconf -e "transport_maps = sqlite:/opt/eu-email-webhook/data/transport.cf"

# Create virtual_domains.cf file
sudo tee /opt/eu-email-webhook/data/virtual_domains.cf > /dev/null <<'EOF'
dbpath = /opt/eu-email-webhook/data/postfix.db
query = SELECT domain FROM virtual_domains WHERE domain='%s' AND active=1
EOF

# Create virtual_aliases.cf file for alias-based routing
sudo tee /opt/eu-email-webhook/data/virtual_aliases.cf > /dev/null <<'EOF'
dbpath = /opt/eu-email-webhook/data/postfix.db
query = SELECT destination FROM virtual_aliases WHERE email='%s' AND active=1
EOF

# Note: transport.cf is no longer needed with the new alias-based spam filtering approach
# The new approach routes emails at the virtual_aliases level based on user plan type:
# - Free users: process-email (direct processing)
# - Pro+ users: spam-process-email (SpamAssassin via spamc)
echo "✅ Transport configuration skipped (using alias-based routing)"

# Set proper permissions for all Postfix configuration files
sudo chown postfix:postfix /opt/eu-email-webhook/data/virtual_domains.cf
sudo chown postfix:postfix /opt/eu-email-webhook/data/virtual_aliases.cf
sudo chown postfix:postfix /opt/eu-email-webhook/data/transport.cf
sudo chmod 644 /opt/eu-email-webhook/data/virtual_domains.cf
sudo chmod 644 /opt/eu-email-webhook/data/virtual_aliases.cf
sudo chmod 644 /opt/eu-email-webhook/data/transport.cf

# Note: Amavis integration is no longer needed with alias-based spam filtering
echo "✅ Amavis integration skipped (using alias-based routing)"

# Create basic alias
echo "process-email: \"|/opt/eu-email-webhook/scripts/production/process-email.js\"" | sudo tee -a /etc/aliases > /dev/null
sudo newaliases

# Wait for Docker containers to initialize the SQLite database
echo "⏳ Waiting for SQLite database initialization..."
sleep 10

# Add test.emailconnect.eu domain to SQLite database for test webhook feature
echo "📧 Adding test.emailconnect.eu domain for test webhook feature..."
if [ -f "/opt/eu-email-webhook/data/postfix.db" ]; then
    sqlite3 /opt/eu-email-webhook/data/postfix.db "INSERT OR IGNORE INTO virtual_domains (domain, destination, active) VALUES ('test.emailconnect.eu', 'process-email', 1);"
    # ADD THIS LINE - the catch-all alias that actually routes emails
    sqlite3 /opt/eu-email-webhook/data/postfix.db "INSERT OR IGNORE INTO virtual_aliases (email, destination, domain, active) VALUES ('@test.emailconnect.eu', 'process-email', 'test.emailconnect.eu', 1);"
    
    echo "✅ test.emailconnect.eu domain and catch-all alias added to SQLite database"
    echo "✅ Alias-based spam filtering configured"
    
    # Reload Postfix to pick up the new configuration
    sudo systemctl reload postfix
    echo "✅ Postfix configuration reloaded"
else
    echo "⚠️  SQLite database not found yet - you may need to add test.emailconnect.eu manually later"
    sudo systemctl reload postfix
fi
echo "✅ Postfix configured"
echo ""

# 11. Status check
echo "📊 Service Status:"
echo ""
echo "Docker Containers:"
cd $APP_DIR
sudo -u $SYSTEM_USER docker compose -f docker-compose.prod.yml ps
echo ""
echo "Postfix:"
sudo systemctl status postfix.service --no-pager -l | head -10
echo ""
echo "SpamAssassin:"
sudo systemctl status spamd.service --no-pager -l | head -5
echo ""
echo "Nginx:"
sudo systemctl status nginx.service --no-pager -l | head -5

echo ""
echo "🎉 Deployment completed!"
echo ""
echo "🔗 API Endpoints:"
echo "  App Health: http://$(hostname -I | awk '{print $1}')/health"
echo "  Postfix Health: http://$(hostname -I | awk '{print $1}')/health/postfix"
echo "  Postfix Status: http://$(hostname -I | awk '{print $1}')/health/postfix/status"
echo "  API: http://$(hostname -I | awk '{print $1}')/api/"
echo ""
echo "📝 Logs:"
echo "  App: docker compose -f $APP_DIR/docker-compose.prod.yml logs -f app"
echo "  Postfix Manager: docker compose -f $APP_DIR/docker-compose.prod.yml logs -f postfix-manager"
echo "  Postfix: journalctl -u postfix.service -f"
echo "  SpamAssassin: journalctl -u spamd.service -f"
echo ""
echo "🧪 Test deployment:"
echo "  curl http://$(hostname -I | awk '{print $1}')/health"
echo "  curl http://$(hostname -I | awk '{print $1}')/health/postfix"
echo "  curl http://$(hostname -I | awk '{print $1}')/health/postfix/status"
echo ""
echo "📝 Next steps:"
echo "  1. Update $APP_DIR/.env.prod with your specific configuration"
echo "  2. Configure your domain's MX record to point to this server"
echo "  3. Set up SSL certificate with certbot"
echo "  4. Test domain addition via API"
echo "  5. Verify test webhook setup:"
echo "     Domain: sqlite3 /opt/eu-email-webhook/data/postfix.db \"SELECT * FROM virtual_domains WHERE domain='test.emailconnect.eu';\""
echo "     Alias: sqlite3 /opt/eu-email-webhook/data/postfix.db \"SELECT * FROM virtual_aliases WHERE email='@test.emailconnect.eu';\""
echo ""
echo "🛡️  Spam Filtering:"
echo "  - SpamAssassin is installed and configured"
echo "  - Free users: emails processed directly (no spam filtering)"
echo "  - Pro+ users: emails processed through SpamAssassin via spamc"
echo "  - Plan-based routing is automatic based on user subscription"
