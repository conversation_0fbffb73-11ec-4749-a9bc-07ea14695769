-- ========================================
-- Table: virtual_domains
-- ========================================
CREATE TABLE IF NOT EXISTS virtual_domains (
    domain VARCHAR(255) PRIMARY KEY,
    destination VARCHAR(255) NOT NULL DEFAULT 'process-email',
    active BOOLEAN DEFAULT 1,
    spam_filtering BOOLEAN DEFAULT 0,                        -- Enable/disable spam filtering
    spam_threshold_green REAL DEFAULT 2.0,                   -- Below: pass through
    spam_threshold_red REAL DEFAULT 5.0,                     -- Above: marked as spam
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance on virtual_domains
CREATE INDEX IF NOT EXISTS idx_virtual_domains_active 
    ON virtual_domains(domain, active);

CREATE INDEX IF NOT EXISTS idx_virtual_domains_spam_filtering 
    ON virtual_domains(domain, spam_filtering, active);

-- ========================================
-- Table: virtual_aliases
-- ========================================
CREATE TABLE IF NOT EXISTS virtual_aliases (
    email VARCHAR(255) PRIMARY KEY,
    destination VARCHAR(255) NOT NULL,
    domain VARCHAR(255),
    active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (domain) REFERENCES virtual_domains(domain) ON DELETE CASCADE
);

-- Indexes for performance on virtual_aliases
CREATE INDEX IF NOT EXISTS idx_virtual_aliases_active
    ON virtual_aliases(email, active);

CREATE INDEX IF NOT EXISTS idx_virtual_aliases_domain
    ON virtual_aliases(domain);

-- Additional indexes for alias-based spam filtering performance
CREATE INDEX IF NOT EXISTS idx_virtual_aliases_email_active
    ON virtual_aliases(email, active);

CREATE INDEX IF NOT EXISTS idx_virtual_aliases_domain_active
    ON virtual_aliases(domain, active);

CREATE INDEX IF NOT EXISTS idx_virtual_aliases_destination
    ON virtual_aliases(destination);

-- Composite index for the main Postfix query
CREATE INDEX IF NOT EXISTS idx_virtual_aliases_lookup
    ON virtual_aliases(email, active, destination);

-- ========================================
-- Triggers
-- ========================================
-- Automatically update 'updated_at' on domain changes
DROP TRIGGER IF EXISTS update_virtual_domains_timestamp;
CREATE TRIGGER IF NOT EXISTS update_virtual_domains_timestamp 
    AFTER UPDATE ON virtual_domains
BEGIN
    UPDATE virtual_domains 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE domain = NEW.domain;
END;

-- Automatically update 'updated_at' on alias changes
CREATE TRIGGER IF NOT EXISTS update_virtual_aliases_timestamp
    AFTER UPDATE ON virtual_aliases
BEGIN
    UPDATE virtual_aliases
    SET updated_at = CURRENT_TIMESTAMP
    WHERE email = NEW.email;
END;

-- ========================================
-- Views for monitoring and administration
-- ========================================

-- View for monitoring spam filtering distribution
CREATE VIEW IF NOT EXISTS spam_filtering_stats AS
SELECT
    destination,
    COUNT(*) as alias_count,
    COUNT(DISTINCT domain) as domain_count,
    CASE
        WHEN destination = 'process-email' THEN 'Free (Direct Processing)'
        WHEN destination = 'spam-process-email' THEN 'Pro+ (Spam Filtering)'
        ELSE 'Other'
    END as processing_type
FROM virtual_aliases
WHERE active = 1
GROUP BY destination;

-- View for domain summary with alias information
CREATE VIEW IF NOT EXISTS domain_summary AS
SELECT
    vd.domain,
    vd.active as domain_active,
    vd.destination as domain_destination,
    COUNT(va.email) as alias_count,
    SUM(CASE WHEN va.destination = 'process-email' THEN 1 ELSE 0 END) as free_aliases,
    SUM(CASE WHEN va.destination = 'spam-process-email' THEN 1 ELSE 0 END) as pro_aliases,
    vd.created_at as domain_created,
    MAX(va.updated_at) as last_alias_update
FROM virtual_domains vd
LEFT JOIN virtual_aliases va ON vd.domain = va.domain AND va.active = 1
WHERE vd.active = 1
GROUP BY vd.domain, vd.active, vd.destination, vd.created_at;
