-- Migration: Add spam filtering support to virtual_domains table
-- Version: 001
-- Description: Adds spam filtering columns to support SpamAssassin integration
-- Date: 2025-01-XX
-- 
-- This migration adds:
-- - spam_filtering: BOOLEAN flag to enable/disable spam filtering per domain
-- - spam_threshold_green: REAL threshold below which emails pass through normally
-- - spam_threshold_red: REAL threshold above which emails are marked as spam
--
-- Usage: Run this script manually on existing installations
-- sqlite3 /opt/eu-email-webhook/data/postfix.db < scripts/migrations/001_add_spam_filtering.sql

BEGIN TRANSACTION;

-- Add spam filtering columns to virtual_domains table
ALTER TABLE virtual_domains ADD COLUMN spam_filtering BOOLEAN DEFAULT 0;
ALTER TABLE virtual_domains ADD COLUMN spam_threshold_green REAL DEFAULT 2.0;
ALTER TABLE virtual_domains ADD COLUMN spam_threshold_red REAL DEFAULT 5.0;

-- Create index for spam filtering queries (performance optimization)
CREATE INDEX IF NOT EXISTS idx_virtual_domains_spam_filtering ON virtual_domains(domain, spam_filtering, active);

-- Update the existing trigger to handle new columns
DROP TRIGGER IF EXISTS update_virtual_domains_timestamp;
CREATE TRIGGER update_virtual_domains_timestamp 
    AFTER UPDATE ON virtual_domains
BEGIN
    UPDATE virtual_domains 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE domain = NEW.domain;
END;

-- Insert migration record (optional - for tracking)
CREATE TABLE IF NOT EXISTS schema_migrations (
    version INTEGER PRIMARY KEY,
    description TEXT NOT NULL,
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO schema_migrations (version, description) 
VALUES (1, 'Add spam filtering support to virtual_domains table');

COMMIT;

-- Verify the migration
.schema virtual_domains
SELECT 'Migration completed successfully. Spam filtering columns added.' as status;
