-- Migration: Update schema for alias-based spam filtering
-- Version: 002
-- Description: Migrates from Amavis transport-based to alias-based spam filtering
-- Date: 2025-06-27
--
-- This migration:
-- - Ensures spam_filtering column exists in virtual_domains (required for new approach)
-- - Cleans up any Amavis-related destinations in virtual_aliases
-- - Updates virtual_aliases destinations to use static values (will be overridden by dynamic queries)
-- - Adds performance indexes for the new approach
-- - Adds migration tracking
--
-- Usage: Run this script on existing installations migrating to alias-based spam filtering
-- sqlite3 /opt/eu-email-webhook/data/postfix.db < scripts/migrations/002_alias_based_spam_filtering.sql

BEGIN TRANSACTION;

-- Create migration tracking table if it doesn't exist
CREATE TABLE IF NOT EXISTS schema_migrations (
    version INTEGER PRIMARY KEY,
    description TEXT NOT NULL,
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Check if this migration has already been applied
-- Note: SQLite doesn't support conditional execution, so we'll use a different approach

-- Ensure spam_filtering column exists in virtual_domains (required for new approach)
-- This is safe to run multiple times
ALTER TABLE virtual_domains ADD COLUMN spam_filtering BOOLEAN DEFAULT 0;
ALTER TABLE virtual_domains ADD COLUMN spam_threshold_green REAL DEFAULT 2.0;
ALTER TABLE virtual_domains ADD COLUMN spam_threshold_red REAL DEFAULT 5.0;

-- Clean up any Amavis-related destinations in virtual_aliases
-- Set them to process-email temporarily (will be overridden by dynamic SQL queries)
UPDATE virtual_aliases
SET destination = 'process-email'
WHERE destination LIKE '%amavis%'
   OR destination LIKE '%127.0.0.1:10024%';

-- Note: The destination column in virtual_aliases is now largely ignored
-- because the Postfix .cf files use dynamic SQL queries based on spam_filtering column
-- However, we keep it for compatibility and debugging purposes

-- Add performance indexes for the new alias-based approach
-- Index for email lookups (most common query)
CREATE INDEX IF NOT EXISTS idx_virtual_aliases_email_active 
    ON virtual_aliases(email, active);

-- Index for domain-based queries (for bulk operations)
CREATE INDEX IF NOT EXISTS idx_virtual_aliases_domain_active 
    ON virtual_aliases(domain, active);

-- Index for destination-based queries (for debugging/monitoring)
CREATE INDEX IF NOT EXISTS idx_virtual_aliases_destination 
    ON virtual_aliases(destination);

-- Composite index for the main Postfix query
CREATE INDEX IF NOT EXISTS idx_virtual_aliases_lookup 
    ON virtual_aliases(email, active, destination);

-- Index for domain lookups in virtual_domains
CREATE INDEX IF NOT EXISTS idx_virtual_domains_lookup 
    ON virtual_domains(domain, active, destination);

-- Add updated_at trigger for virtual_aliases if it doesn't exist
-- (This might already exist from previous migrations)
CREATE TRIGGER IF NOT EXISTS update_virtual_aliases_timestamp 
    AFTER UPDATE ON virtual_aliases
BEGIN
    UPDATE virtual_aliases 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE email = NEW.email;
END;

-- Add updated_at trigger for virtual_domains if it doesn't exist
CREATE TRIGGER IF NOT EXISTS update_virtual_domains_timestamp 
    AFTER UPDATE ON virtual_domains
BEGIN
    UPDATE virtual_domains 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE domain = NEW.domain;
END;

-- Optional: Add a view for monitoring spam filtering distribution
-- This helps administrators see how many domains are using each processing method
CREATE VIEW IF NOT EXISTS spam_filtering_stats AS
SELECT 
    destination,
    COUNT(*) as alias_count,
    COUNT(DISTINCT domain) as domain_count
FROM virtual_aliases 
WHERE active = 1
GROUP BY destination;

-- Optional: Add a view for domain summary
CREATE VIEW IF NOT EXISTS domain_summary AS
SELECT 
    vd.domain,
    vd.active as domain_active,
    vd.destination as domain_destination,
    COUNT(va.email) as alias_count,
    GROUP_CONCAT(va.destination) as alias_destinations
FROM virtual_domains vd
LEFT JOIN virtual_aliases va ON vd.domain = va.domain AND va.active = 1
WHERE vd.active = 1
GROUP BY vd.domain, vd.active, vd.destination;

-- Record this migration
INSERT OR IGNORE INTO schema_migrations (version, description)
VALUES (2, 'Migrated from Amavis transport-based to alias-based spam filtering');

COMMIT;

-- Display migration results
.echo on
SELECT 'Migration 002 completed successfully.' as status;
SELECT 'Migrated to alias-based spam filtering approach.' as message;

-- Show domains with spam filtering enabled
SELECT 'Domains with spam filtering enabled:' as info;
SELECT domain, spam_filtering, destination, created_at
FROM virtual_domains
WHERE active = 1 AND spam_filtering = 1;

-- Show current alias destination distribution (note: destinations will be overridden by dynamic queries)
SELECT 'Current alias destination distribution (static values, overridden by dynamic queries):' as info;
SELECT destination, COUNT(*) as count
FROM virtual_aliases
WHERE active = 1
GROUP BY destination;

-- Show any remaining Amavis references (should be none after cleanup)
SELECT 'Checking for remaining Amavis references:' as check;
SELECT email, destination
FROM virtual_aliases
WHERE destination LIKE '%amavis%'
   OR destination LIKE '%127.0.0.1%';

-- Show spam filtering statistics
SELECT 'Spam filtering statistics:' as stats;
SELECT
    spam_filtering,
    COUNT(*) as domain_count,
    CASE
        WHEN spam_filtering = 1 THEN 'Pro+ (Spam Filtering Enabled)'
        ELSE 'Free (Direct Processing)'
    END as plan_type
FROM virtual_domains
WHERE active = 1
GROUP BY spam_filtering;

.echo off
