import { FastifyPluginAsync } from 'fastify';
import { requireAuth } from '../lib/auth.js';
import { NotificationService } from '../services/notifications/notification.service.js';

export const notificationsRoutes: FastifyPluginAsync = async (fastify) => {
  const errorResponseSchema = { $ref: 'ErrorResponse#' };

  // Get user's notifications
  fastify.get('/notifications', {
    preHandler: [requireAuth()],
    schema: {
      tags: ['Notifications'],
      summary: 'Get user notifications',
      description: 'Retrieves notifications for the authenticated user.',
      querystring: {
        type: 'object',
        properties: {
          unreadOnly: { type: 'boolean', default: false },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 20 },
          offset: { type: 'integer', minimum: 0, default: 0 }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            notifications: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  type: { type: 'string' },
                  title: { type: 'string' },
                  message: { type: 'string' },
                  category: { type: 'string' },
                  priority: { type: 'string' },
                  data: { type: 'object' },
                  actionUrl: { type: 'string' },
                  actionText: { type: 'string' },
                  isRead: { type: 'boolean' },
                  readAt: { type: 'string' },
                  createdAt: { type: 'string' },
                  expiresAt: { type: 'string' }
                }
              }
            },
            total: { type: 'integer' },
            unreadCount: { type: 'integer' }
          }
        },
        401: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const user = (request as any).user;
      const { unreadOnly = false, limit = 20, offset = 0 } = request.query as any;

      const [notifications, unreadCount] = await Promise.all([
        NotificationService.getUserNotifications(user.id, { unreadOnly, limit, offset }),
        NotificationService.getUnreadCount(user.id)
      ]);

      return reply.send({
        success: true,
        notifications,
        total: notifications.length,
        unreadCount
      });
    } catch (error) {
      fastify.log.error(error, 'Failed to get notifications');
      return reply.code(500).send({
        success: false,
        error: 'Failed to retrieve notifications'
      });
    }
  });

  // Get unread notification count
  fastify.get('/notifications/unread-count', {
    preHandler: [requireAuth()],
    schema: {
      tags: ['Notifications'],
      summary: 'Get unread notification count',
      description: 'Returns the count of unread notifications for the authenticated user.',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            unreadCount: { type: 'integer' }
          }
        },
        401: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const user = (request as any).user;
      const unreadCount = await NotificationService.getUnreadCount(user.id);

      return reply.send({
        success: true,
        unreadCount
      });
    } catch (error) {
      fastify.log.error(error, 'Failed to get unread count');
      return reply.code(500).send({
        success: false,
        error: 'Failed to get unread count'
      });
    }
  });

  // Mark notification as read
  fastify.patch('/notifications/:notificationId/read', {
    preHandler: [requireAuth()],
    schema: {
      tags: ['Notifications'],
      summary: 'Mark notification as read',
      description: 'Marks a specific notification as read for the authenticated user.',
      params: {
        type: 'object',
        properties: {
          notificationId: { type: 'string' }
        },
        required: ['notificationId']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        401: errorResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const user = (request as any).user;
      const { notificationId } = request.params as any;

      await NotificationService.markAsRead(notificationId, user.id);

      return reply.send({
        success: true,
        message: 'Notification marked as read'
      });
    } catch (error) {
      fastify.log.error(error, 'Failed to mark notification as read');
      return reply.code(500).send({
        success: false,
        error: 'Failed to mark notification as read'
      });
    }
  });

  // Mark all notifications as read
  fastify.patch('/notifications/mark-all-read', {
    preHandler: [requireAuth()],
    schema: {
      tags: ['Notifications'],
      summary: 'Mark all notifications as read',
      description: 'Marks all unread notifications as read for the authenticated user.',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        401: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const user = (request as any).user;
      await NotificationService.markAllAsRead(user.id);

      return reply.send({
        success: true,
        message: 'All notifications marked as read'
      });
    } catch (error) {
      fastify.log.error(error, 'Failed to mark all notifications as read');
      return reply.code(500).send({
        success: false,
        error: 'Failed to mark all notifications as read'
      });
    }
  });

  // Delete old notifications (admin/cleanup endpoint)
  fastify.delete('/notifications/cleanup', {
    preHandler: [requireAuth()],
    schema: {
      tags: ['Notifications'],
      summary: 'Cleanup old notifications',
      description: 'Deletes old read notifications (older than specified days).',
      querystring: {
        type: 'object',
        properties: {
          olderThanDays: { type: 'integer', minimum: 1, maximum: 365, default: 30 }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        401: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const { olderThanDays = 30 } = request.query as any;
      await NotificationService.deleteOldNotifications(olderThanDays);

      return reply.send({
        success: true,
        message: `Old notifications deleted (older than ${olderThanDays} days)`
      });
    } catch (error) {
      fastify.log.error(error, 'Failed to cleanup notifications');
      return reply.code(500).send({
        success: false,
        error: 'Failed to cleanup notifications'
      });
    }
  });
};
