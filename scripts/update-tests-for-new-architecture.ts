#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to update test files for the new architecture (no domain webhooks)
 * 
 * This script updates common patterns in test files:
 * 1. createTestDomain(userId, webhookId, data) -> createTestDomainWithCatchAll(userId, webhookId, data)
 * 2. Remove domain webhook references
 * 3. Update Prisma queries that reference domain.webhook
 * 
 * Usage:
 *   npx tsx scripts/update-tests-for-new-architecture.ts
 */

import { readFileSync, writeFileSync, readdirSync, statSync } from 'fs';
import { join } from 'path';

const testDir = 'tests';

function getAllTestFiles(dir: string): string[] {
  const files: string[] = [];
  
  function traverse(currentDir: string) {
    const items = readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = join(currentDir, item);
      const stat = statSync(fullPath);
      
      if (stat.isDirectory()) {
        traverse(fullPath);
      } else if (item.endsWith('.test.ts') || item.endsWith('.test.js')) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

function updateTestFile(filePath: string): boolean {
  console.log(`Processing: ${filePath}`);
  
  let content = readFileSync(filePath, 'utf-8');
  let modified = false;
  
  // 1. Add createTestDomainWithCatchAll import if createTestDomain is imported
  if (content.includes('createTestDomain') && !content.includes('createTestDomainWithCatchAll')) {
    content = content.replace(
      /import\s*{\s*([^}]*createTestDomain[^}]*)\s*}\s*from\s*['"][^'"]*test-db-setup[^'"]*['"];?/,
      (match, imports) => {
        if (!imports.includes('createTestDomainWithCatchAll')) {
          const newImports = imports.trim() + ',\n  createTestDomainWithCatchAll';
          return match.replace(imports, newImports);
        }
        return match;
      }
    );
    modified = true;
  }
  
  // 2. Replace createTestDomain(userId, webhookId, data) calls
  const createDomainPattern = /createTestDomain\s*\(\s*([^,]+),\s*([^,]+),\s*({[^}]*}|\{[^}]*\})\s*\)/g;
  const newContent = content.replace(createDomainPattern, (match, userId, webhookId, data) => {
    console.log(`  - Replacing createTestDomain call`);
    return `createTestDomainWithCatchAll(${userId}, ${webhookId}, ${data})`;
  });
  
  if (newContent !== content) {
    content = newContent;
    modified = true;
  }
  
  // 3. Replace simple createTestDomain calls without data object
  const simpleDomainPattern = /createTestDomain\s*\(\s*([^,]+),\s*([^,)]+)\s*\)/g;
  const newContent2 = content.replace(simpleDomainPattern, (match, userId, webhookId) => {
    console.log(`  - Replacing simple createTestDomain call`);
    return `createTestDomainWithCatchAll(${userId}, ${webhookId})`;
  });
  
  if (newContent2 !== content) {
    content = newContent2;
    modified = true;
  }
  
  // 4. Remove domain webhook includes from Prisma queries
  const webhookIncludePattern = /include:\s*{\s*([^}]*),?\s*webhook:\s*true\s*,?\s*([^}]*)\s*}/g;
  const newContent3 = content.replace(webhookIncludePattern, (match, before, after) => {
    console.log(`  - Removing webhook include from Prisma query`);
    const cleanBefore = before.trim().replace(/,$/, '');
    const cleanAfter = after.trim().replace(/^,/, '');
    
    if (cleanBefore && cleanAfter) {
      return `include: { ${cleanBefore}, ${cleanAfter} }`;
    } else if (cleanBefore) {
      return `include: { ${cleanBefore} }`;
    } else if (cleanAfter) {
      return `include: { ${cleanAfter} }`;
    } else {
      return '';
    }
  });
  
  if (newContent3 !== content) {
    content = newContent3;
    modified = true;
  }
  
  // 5. Remove standalone webhook includes
  const standaloneWebhookPattern = /include:\s*{\s*webhook:\s*true\s*}/g;
  const newContent4 = content.replace(standaloneWebhookPattern, () => {
    console.log(`  - Removing standalone webhook include`);
    return '';
  });
  
  if (newContent4 !== content) {
    content = newContent4;
    modified = true;
  }
  
  // 6. Comment out domain.webhook property accesses
  const webhookAccessPattern = /(\s*)(expect\([^)]*\.webhook[^)]*\)[^;]*;)/g;
  const newContent5 = content.replace(webhookAccessPattern, (match, indent, expectStatement) => {
    console.log(`  - Commenting out domain.webhook access`);
    return `${indent}// ${expectStatement} // TODO: Update for new architecture`;
  });
  
  if (newContent5 !== content) {
    content = newContent5;
    modified = true;
  }
  
  // 7. Comment out domain.webhookId property accesses
  const webhookIdAccessPattern = /(\s*)(expect\([^)]*\.webhookId[^)]*\)[^;]*;)/g;
  const newContent6 = content.replace(webhookIdAccessPattern, (match, indent, expectStatement) => {
    console.log(`  - Commenting out domain.webhookId access`);
    return `${indent}// ${expectStatement} // TODO: Update for new architecture`;
  });
  
  if (newContent6 !== content) {
    content = newContent6;
    modified = true;
  }
  
  if (modified) {
    writeFileSync(filePath, content, 'utf-8');
    console.log(`  ✅ Updated ${filePath}`);
    return true;
  } else {
    console.log(`  ⏭️  No changes needed for ${filePath}`);
    return false;
  }
}

async function main() {
  console.log('🔄 Updating test files for new architecture...\n');
  
  const testFiles = getAllTestFiles(testDir);
  console.log(`Found ${testFiles.length} test files\n`);
  
  let updatedCount = 0;
  
  for (const file of testFiles) {
    try {
      if (updateTestFile(file)) {
        updatedCount++;
      }
    } catch (error) {
      console.error(`❌ Error processing ${file}:`, error);
    }
  }
  
  console.log(`\n🎉 Updated ${updatedCount} out of ${testFiles.length} test files`);
  console.log('\nNext steps:');
  console.log('- Review the changes and fix any remaining issues manually');
  console.log('- Run npm test to see remaining failures');
  console.log('- Update test expectations for the new architecture');
}

main().catch(console.error);
