-- Migration: Update schema for alias-based spam filtering
-- Version: 002
-- Description: Updates SQLite schema to support the new alias-based spam filtering approach
-- Date: 2025-01-XX
-- 
-- This migration:
-- - Removes the need for spam_filtering column in virtual_domains (no longer used)
-- - Adds performance indexes for the new approach
-- - Cleans up any Amavis-related data
-- - Adds migration tracking
--
-- Usage: Run this script on existing installations migrating to alias-based spam filtering
-- sqlite3 /opt/eu-email-webhook/data/postfix.db < scripts/migrations/002_alias_based_spam_filtering.sql

BEGIN TRANSACTION;

-- Create migration tracking table if it doesn't exist
CREATE TABLE IF NOT EXISTS schema_migrations (
    version INTEGER PRIMARY KEY,
    description TEXT NOT NULL,
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Check if this migration has already been applied
-- Note: SQLite doesn't support conditional execution, so we'll use a different approach

-- Clean up any Amavis-related destinations in virtual_aliases
UPDATE virtual_aliases 
SET destination = 'process-email' 
WHERE destination LIKE '%amavis%' 
   OR destination LIKE '%127.0.0.1:10024%'
   OR destination LIKE '%spam-process-email%';

-- Note: We intentionally set everything to 'process-email' initially
-- The main application will update these to 'spam-process-email' for Pro+ users
-- when it syncs the plan information

-- Add performance indexes for the new alias-based approach
-- Index for email lookups (most common query)
CREATE INDEX IF NOT EXISTS idx_virtual_aliases_email_active 
    ON virtual_aliases(email, active);

-- Index for domain-based queries (for bulk operations)
CREATE INDEX IF NOT EXISTS idx_virtual_aliases_domain_active 
    ON virtual_aliases(domain, active);

-- Index for destination-based queries (for debugging/monitoring)
CREATE INDEX IF NOT EXISTS idx_virtual_aliases_destination 
    ON virtual_aliases(destination);

-- Composite index for the main Postfix query
CREATE INDEX IF NOT EXISTS idx_virtual_aliases_lookup 
    ON virtual_aliases(email, active, destination);

-- Index for domain lookups in virtual_domains
CREATE INDEX IF NOT EXISTS idx_virtual_domains_lookup 
    ON virtual_domains(domain, active, destination);

-- Add updated_at trigger for virtual_aliases if it doesn't exist
-- (This might already exist from previous migrations)
CREATE TRIGGER IF NOT EXISTS update_virtual_aliases_timestamp 
    AFTER UPDATE ON virtual_aliases
BEGIN
    UPDATE virtual_aliases 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE email = NEW.email;
END;

-- Add updated_at trigger for virtual_domains if it doesn't exist
CREATE TRIGGER IF NOT EXISTS update_virtual_domains_timestamp 
    AFTER UPDATE ON virtual_domains
BEGIN
    UPDATE virtual_domains 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE domain = NEW.domain;
END;

-- Optional: Add a view for monitoring spam filtering distribution
-- This helps administrators see how many domains are using each processing method
CREATE VIEW IF NOT EXISTS spam_filtering_stats AS
SELECT 
    destination,
    COUNT(*) as alias_count,
    COUNT(DISTINCT domain) as domain_count
FROM virtual_aliases 
WHERE active = 1
GROUP BY destination;

-- Optional: Add a view for domain summary
CREATE VIEW IF NOT EXISTS domain_summary AS
SELECT 
    vd.domain,
    vd.active as domain_active,
    vd.destination as domain_destination,
    COUNT(va.email) as alias_count,
    GROUP_CONCAT(va.destination) as alias_destinations
FROM virtual_domains vd
LEFT JOIN virtual_aliases va ON vd.domain = va.domain AND va.active = 1
WHERE vd.active = 1
GROUP BY vd.domain, vd.active, vd.destination;

-- Record this migration
INSERT OR IGNORE INTO schema_migrations (version, description) 
VALUES (2, 'Update schema for alias-based spam filtering approach');

COMMIT;

-- Display migration results
.echo on
SELECT 'Migration 002 completed successfully.' as status;
SELECT 'Alias-based spam filtering schema updated.' as message;

-- Show current alias distribution
SELECT 'Current alias destination distribution:' as info;
SELECT destination, COUNT(*) as count 
FROM virtual_aliases 
WHERE active = 1 
GROUP BY destination;

-- Show any remaining Amavis references (should be none)
SELECT 'Checking for remaining Amavis references:' as check;
SELECT email, destination 
FROM virtual_aliases 
WHERE destination LIKE '%amavis%' 
   OR destination LIKE '%127.0.0.1%';

.echo off
