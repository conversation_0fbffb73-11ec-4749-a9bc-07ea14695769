#!/usr/bin/env node

/**
 * Spam-aware email processor script called by Postfix for Pro+ users
 * This script reads raw email from stdin, processes it through SpamAssassin,
 * and forwards it to the main application with spam headers included
 *
 * Flow: Postfix → spam-process-email → SpamAssassin (spamc) → Main App
 * Used for: Pro and Enterprise users only
 * Free users use: process-email.js (direct processing)
 */

import http from 'http';
import { spawn } from 'child_process';
import { PrismaClient } from '@prisma/client';

// Configuration - use environment variables or defaults
const APP_HOST = process.env.APP_HOST || 'localhost';
const APP_PORT = process.env.APP_PORT || 3000;
const TIMEOUT_MS = parseInt(process.env.WEBHOOK_TIMEOUT_MS) || 30000;
const SPAMC_TIMEOUT = parseInt(process.env.SPAMC_TIMEOUT_MS) || 10000;

// SpamAssassin configuration
const SPAMC_PATH = process.env.SPAMC_PATH || '/usr/bin/spamc';

// Database configuration
const prisma = new PrismaClient({
  datasourceUrl: process.env.DATABASE_URL || 'postgresql://postgres:password@localhost:5432/eu_email_webhook'
});

/**
 * Extract domain from email headers
 * @param {string} rawEmail - Raw email content
 * @returns {string|null} - Extracted domain or null
 */
function extractDomainFromEmail(rawEmail) {
  try {
    const lines = rawEmail.split('\n');

    // Look for X-Original-To header first (most reliable for our setup)
    for (const line of lines) {
      if (line.toLowerCase().startsWith('x-original-to:')) {
        const email = line.split(':')[1]?.trim();
        if (email && email.includes('@')) {
          return email.split('@')[1];
        }
      }
    }

    // Fallback to Delivered-To header
    for (const line of lines) {
      if (line.toLowerCase().startsWith('delivered-to:')) {
        const email = line.split(':')[1]?.trim();
        if (email && email.includes('@')) {
          return email.split('@')[1];
        }
      }
    }

    // Fallback to To header
    for (const line of lines) {
      if (line.toLowerCase().startsWith('to:')) {
        const email = line.split(':')[1]?.trim();
        if (email && email.includes('@')) {
          return email.split('@')[1];
        }
      }
    }

    return null;
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error extracting domain: ${error.message}`);
    return null;
  }
}

/**
 * Get spam filtering thresholds for a domain from Postgres
 * @param {string} domain - Domain name
 * @returns {Promise<{green: number, red: number}>} - Spam thresholds
 */
async function getDomainThresholds(domain) {
  try {
    const domainRecord = await prisma.domain.findFirst({
      where: { domain: domain },
      select: { configuration: true }
    });

    if (!domainRecord) {
      console.log(`[${new Date().toISOString()}] Domain ${domain} not found, using default thresholds`);
      return { green: 2.0, red: 5.0 };
    }

    const config = domainRecord.configuration || {};
    const spamConfig = config.spamFiltering || {};
    const thresholds = spamConfig.thresholds || {};

    const result = {
      green: thresholds.green || 2.0,
      red: thresholds.red || 5.0
    };

    console.log(`[${new Date().toISOString()}] Domain ${domain} thresholds: green=${result.green}, red=${result.red}`);
    return result;
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error fetching thresholds for ${domain}: ${error.message}`);
    return { green: 2.0, red: 5.0 }; // Default fallback
  }
}

/**
 * Extract spam score from SpamAssassin processed email
 * @param {string} processedEmail - Email with spam headers
 * @returns {number} - Spam score or 0 if not found
 */
function extractSpamScore(processedEmail) {
  try {
    const lines = processedEmail.split('\n');

    for (const line of lines) {
      if (line.toLowerCase().startsWith('x-spam-status:')) {
        const scoreMatch = line.match(/score=([\d.-]+)/);
        if (scoreMatch) {
          return parseFloat(scoreMatch[1]);
        }
      }
    }

    return 0;
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error extracting spam score: ${error.message}`);
    return 0;
  }
}

/**
 * Process email through SpamAssassin using spamc with custom thresholds
 * @param {string} rawEmail - Raw email content
 * @param {Object} thresholds - Spam thresholds {green, red}
 * @returns {Promise<string|null>} - Email with spam headers added, or null if discarded
 */
function processWithSpamAssassin(rawEmail, thresholds) {
  return new Promise((resolve, reject) => {
    // Use red threshold for SpamAssassin's required score
    const spamcArgs = ['-t', thresholds.red.toString()];

    const spamc = spawn(SPAMC_PATH, spamcArgs, {
      stdio: ['pipe', 'pipe', 'pipe'],
      timeout: SPAMC_TIMEOUT
    });

    let spamProcessedEmail = '';
    let errorOutput = '';

    // Send email to SpamAssassin
    spamc.stdin.write(rawEmail);
    spamc.stdin.end();

    // Collect processed email
    spamc.stdout.on('data', (chunk) => {
      spamProcessedEmail += chunk.toString();
    });

    // Collect error output
    spamc.stderr.on('data', (chunk) => {
      errorOutput += chunk.toString();
    });

    // Handle completion
    spamc.on('close', (code) => {
      if (code === 0) {
        // SpamAssassin processed successfully
        console.log(`[${new Date().toISOString()}] SpamAssassin processing completed (exit code: ${code})`);

        // Extract spam score and implement three-tier filtering
        const spamScore = extractSpamScore(spamProcessedEmail);
        console.log(`[${new Date().toISOString()}] Spam score: ${spamScore}, thresholds: green=${thresholds.green}, red=${thresholds.red}`);

        if (spamScore > thresholds.red) {
          // RED ZONE: Discard email entirely
          console.log(`[${new Date().toISOString()}] Email discarded: spam score ${spamScore} > red threshold ${thresholds.red}`);
          resolve(null); // Return null to indicate email should be discarded
        } else if (spamScore > thresholds.green) {
          // ORANGE ZONE: Mark as spam but deliver
          console.log(`[${new Date().toISOString()}] Email marked as spam: score ${spamScore} > green threshold ${thresholds.green}`);
          resolve(spamProcessedEmail);
        } else {
          // GREEN ZONE: Pass through with spam headers
          console.log(`[${new Date().toISOString()}] Email passed spam filtering: score ${spamScore} <= green threshold ${thresholds.green}`);
          resolve(spamProcessedEmail);
        }
      } else {
        // SpamAssassin failed, but don't block email delivery
        console.warn(`[${new Date().toISOString()}] SpamAssassin processing failed (exit code: ${code}), continuing with original email`);
        console.warn(`[${new Date().toISOString()}] SpamAssassin error: ${errorOutput}`);
        // Return original email if SpamAssassin fails
        resolve(rawEmail);
      }
    });

    // Handle timeout
    spamc.on('error', (error) => {
      console.error(`[${new Date().toISOString()}] SpamAssassin process error: ${error.message}`);
      // Return original email if SpamAssassin fails
      resolve(rawEmail);
    });

    // Set timeout for the entire process
    setTimeout(() => {
      if (!spamc.killed) {
        console.warn(`[${new Date().toISOString()}] SpamAssassin timeout after ${SPAMC_TIMEOUT}ms, killing process`);
        spamc.kill('SIGTERM');
        // Return original email on timeout
        resolve(rawEmail);
      }
    }, SPAMC_TIMEOUT);
  });
}

/**
 * Forward processed email to main application
 * @param {string} processedEmail - Email with spam headers
 */
function forwardToMainApp(processedEmail) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: APP_HOST,
      port: APP_PORT,
      path: '/api/email/process',
      method: 'POST',
      headers: {
        'Content-Type': 'text/plain',
        'Content-Length': Buffer.byteLength(processedEmail),
        'X-Email-Source': 'postfix',
        'X-Processing-Script': '/opt/eu-email-webhook/scripts/production/spam-process-email.js',
        'X-Spam-Processed': 'true', // Indicate this email was processed for spam
      },
      timeout: TIMEOUT_MS,
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          console.log(`[${new Date().toISOString()}] Spam-processed email delivered successfully (${res.statusCode})`);
          resolve(responseData);
        } else {
          console.error(`[${new Date().toISOString()}] Email processing failed: ${res.statusCode} ${responseData}`);
          reject(new Error(`HTTP ${res.statusCode}: ${responseData}`));
        }
      });
    });

    req.on('timeout', () => {
      console.error(`[${new Date().toISOString()}] Email processing timeout after ${TIMEOUT_MS}ms`);
      req.destroy();
      reject(new Error(`Timeout after ${TIMEOUT_MS}ms`));
    });

    req.on('error', (error) => {
      console.error(`[${new Date().toISOString()}] Failed to forward email:`, error.message);
      reject(error);
    });

    req.write(processedEmail);
    req.end();
  });
}

// Main processing logic
async function main() {
  try {
    // Read email from stdin (pipe from Postfix)
    let rawEmail = '';
    
    process.stdin.setEncoding('utf8');
    
    // Collect all input
    for await (const chunk of process.stdin) {
      rawEmail += chunk;
    }

    if (!rawEmail.trim()) {
      console.error(`[${new Date().toISOString()}] No email content received from stdin`);
      process.exit(1);
    }

    console.log(`[${new Date().toISOString()}] Processing email for spam filtering (${rawEmail.length} bytes)`);

    // Step 1: Extract domain from email
    const domain = extractDomainFromEmail(rawEmail);
    if (!domain) {
      console.error(`[${new Date().toISOString()}] Could not extract domain from email, using default thresholds`);
    }

    // Step 2: Get domain-specific spam thresholds
    const thresholds = await getDomainThresholds(domain);

    // Step 3: Process through SpamAssassin with custom thresholds
    const spamProcessedEmail = await processWithSpamAssassin(rawEmail, thresholds);

    // Step 4: Check if email was discarded (null return)
    if (spamProcessedEmail === null) {
      console.log(`[${new Date().toISOString()}] Email discarded due to high spam score, not forwarding to main app`);
      process.exit(0); // Exit successfully but don't forward email
    }

    // Step 5: Forward to main application
    await forwardToMainApp(spamProcessedEmail);
    
    console.log(`[${new Date().toISOString()}] Email processing completed successfully`);
    process.exit(0);

  } catch (error) {
    console.error(`[${new Date().toISOString()}] Email processing error:`, error.message);
    process.exit(1);
  }
}

// Handle process termination gracefully
process.on('SIGTERM', () => {
  console.log(`[${new Date().toISOString()}] Received SIGTERM, shutting down gracefully`);
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log(`[${new Date().toISOString()}] Received SIGINT, shutting down gracefully`);
  process.exit(0);
});

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  console.error(`[${new Date().toISOString()}] Uncaught exception:`, error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(`[${new Date().toISOString()}] Unhandled rejection at:`, promise, 'reason:', reason);
  process.exit(1);
});

// Start processing
main();
