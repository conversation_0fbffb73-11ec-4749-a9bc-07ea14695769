<template>
  <div class="card bg-base-100">
    <div class="card-body">
      <h2 class="card-title mb-6">
        <span class="badge badge-success bg-primary/10 text-base-content badge-sm badge-outline">Pro</span>
        Spam filtering
      </h2>
      
      <!-- Free Plan Limitations - Only show for free users -->
      <div v-if="!isProUser" class="bg-info/10 border border-info/20 rounded-lg p-4 mb-6">
        <h4 class="text-sm font-semibold text-info mb-2">Free plan limitations</h4>
        <div class="space-y-2 text-sm text-base-content/70">
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>No spam filtering available</span>
          </div>
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>All emails processed directly without spam analysis</span>
          </div>
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>No spam scores or reports in webhook payloads</span>
          </div>
        </div>
      </div>

      <!-- Success Message -->
      <div v-if="showSuccessMessage" class="bg-success/10 border border-success/20 rounded-lg p-3 mb-4">
        <div class="flex items-start gap-2">
          <svg class="w-4 h-4 text-success mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
          <div class="text-sm text-success">
            <strong>Spam filtering settings saved successfully!</strong>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="showErrorMessage" class="bg-error/10 border border-error/20 rounded-lg p-3 mb-4">
        <div class="flex items-start gap-2">
          <svg class="w-4 h-4 text-error mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div class="text-sm text-error">
            <strong>Error:</strong> {{ errorMessage }}
          </div>
        </div>
      </div>

      <!-- Spam Filtering Configuration -->
      <div class="bg-base-200/40 rounded-lg p-6">
        <h3 class="text-lg font-semibold mb-4 flex items-center gap-2">
          <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          SpamAssassin integration
        </h3>
        
        <div v-if="!isProUser" class="mb-6">
          <p class="text-sm text-base-content/70 mb-2">
            Upgrade to Pro to unlock advanced spam filtering powered by SpamAssassin
          </p>
          <div class="bg-base-300/50 rounded-lg p-4">
            <h4 class="text-sm font-semibold mb-2">Pro spam filtering includes:</h4>
            <ul class="text-sm text-base-content/70 space-y-1">
              <li>• Real-time spam analysis with SpamAssassin</li>
              <li>• Spam scores and detailed reports in webhook payloads</li>
              <li>• Configurable spam filtering per domain</li>
              <li>• Advanced spam detection rules and machine learning</li>
            </ul>
          </div>
        </div>
        
        <div v-else class="mb-6">
          <p class="text-sm text-base-content/70 mb-2">
            Configure spam filtering for your domains. When enabled, emails are processed through SpamAssassin 
            before being sent to your webhooks, providing spam scores and detailed analysis.
          </p>
        </div>

        <!-- Domain Spam Filtering Toggles -->
        <div v-if="domains.length > 0" class="space-y-4">
          <h4 class="text-sm font-semibold">Domain spam filtering</h4>
          
          <div class="space-y-3">
            <div 
              v-for="domain in domains" 
              :key="domain.id"
              class="flex items-center justify-between p-4 bg-base-100 rounded-lg border border-base-300"
              :class="{ 'opacity-60': !isProUser || loading }"
            >
              <div class="flex-1">
                <div class="font-medium">{{ domain.domain }}</div>
                <div class="text-sm text-base-content/60">
                  {{ domain.spamFiltering ? 'Emails processed through SpamAssassin' : 'Direct email processing (no spam filtering)' }}
                </div>
              </div>
              
              <div class="flex items-center space-x-3">
                <div class="form-control">
                  <label class="label cursor-pointer">
                    <input
                      type="checkbox"
                      :checked="domain.spamFiltering"
                      @change="toggleSpamFiltering(domain)"
                      :disabled="!isProUser || loading"
                      class="toggle toggle-primary"
                    />
                  </label>
                </div>
              </div>
            </div>
          </div>
          
          <div v-if="!isProUser" class="text-sm text-base-content/60 mt-4">
            <router-link to="/settings#billing" class="link">Upgrade to Pro</router-link> to enable spam filtering for your domains
          </div>
        </div>
        
        <!-- No Domains Message -->
        <div v-else class="text-center py-8">
          <svg class="w-12 h-12 text-base-content/30 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2M4 13h2m13-8l-4 4m0 0l-4-4m4 4V3" />
          </svg>
          <p class="text-base-content/60 mb-2">No domains configured</p>
          <p class="text-sm text-base-content/50">Add domains to configure spam filtering settings</p>
          <router-link to="/domains" class="btn btn-primary btn-sm mt-4">
            Add Domain
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useMetrics } from '../../composables/useMetrics'
import { useDomainApi } from '../../composables/useApi'

// Composables
const { metricsData, loadMetrics } = useMetrics()
const { updateDomain } = useDomainApi()

// Local state for domains
const domains = ref([])
const loading = ref(false)

// Local state
const showSuccessMessage = ref(false)
const showErrorMessage = ref(false)
const errorMessage = ref('')

// Check if user has Pro plan or higher
const isProUser = computed(() => {
  const planType = metricsData.value?.user?.planType || 'free'
  return planType === 'pro' || planType === 'enterprise'
})

// Load domains data
const loadDomains = async () => {
  try {
    loading.value = true
    const response = await fetch('/api/domains')
    const data = await response.json()
    domains.value = data.domains || []
  } catch (error) {
    console.error('Failed to load domains:', error)
    errorMessage.value = 'Failed to load domains. Please refresh the page.'
    showErrorMessage.value = true
  } finally {
    loading.value = false
  }
}

// Methods
const toggleSpamFiltering = async (domain: any) => {
  try {
    showSuccessMessage.value = false
    showErrorMessage.value = false

    const newSpamFiltering = !domain.spamFiltering

    await updateDomain(domain.id, {
      spamFiltering: newSpamFiltering
    })

    // Update local state
    domain.spamFiltering = newSpamFiltering

    // Show success message
    showSuccessMessage.value = true
    setTimeout(() => {
      showSuccessMessage.value = false
    }, 3000)
  } catch (err: any) {
    console.error('Failed to update spam filtering:', err)
    errorMessage.value = err.message || 'Failed to update spam filtering. Please try again.'
    showErrorMessage.value = true
    setTimeout(() => {
      showErrorMessage.value = false
    }, 5000)
  }
}

onMounted(async () => {
  // Load user metrics and domains
  await Promise.all([
    loadMetrics(),
    loadDomains()
  ])
})
</script>

<style scoped>
.toggle:checked {
  background-color: hsl(var(--p));
  border-color: hsl(var(--p));
}
</style>
