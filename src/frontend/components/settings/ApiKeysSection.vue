<template>
  <div class="card bg-base-100">
    <div class="card-body">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h2 class="card-title">API keys</h2>
          <p class="mt-1 text-base-content/70">
            Manage your API keys for programmatic access to your assets.
          </p>
        </div>
        <button
          @click="showCreateApiKeyModal = true"
          class="btn btn-primary"
        >
          Generate key
        </button>
      </div>

      <!-- API Keys List -->
      <div v-if="apiKeys.length > 0" class="bg-base-200/40 rounded-lg p-6">
        <div class="space-y-3">
          <div
            v-for="apiKey in apiKeys"
            :key="apiKey.id"
            class="flex items-center justify-between p-4 bg-base-100 rounded-lg"
          >
            <div class="flex-1">
              <h3 class="font-medium">{{ apiKey.name }}</h3>
              <p class="text-sm text-base-content/70 font-mono">{{ apiKey.keyPrefix }}</p>
              <p class="text-xs text-base-content/50 mb-2">
                Created {{ formatDate(apiKey.createdAt) }}
                <span v-if="apiKey.lastUsedAt">
                  • Last used {{ formatDate(apiKey.lastUsedAt) }}
                </span>
                <span v-else>
                  • Never used
                </span>
              </p>
              <div v-if="apiKey.description" class="text-xs text-base-content/60 mb-2">
                {{ apiKey.description }}
              </div>
              <div class="flex flex-wrap gap-1">
                <span 
                  v-for="scope in apiKey.scopes" 
                  :key="scope"
                  class="badge badge-outline badge-xs"
                  :class="getScopeBadgeClass(scope)"
                >
                  {{ scope }}
                </span>
              </div>
            </div>
            <button
              @click="revokeApiKey(apiKey)"
              class="btn btn-outline btn-error btn-sm"
            >
              Revoke
            </button>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else class="bg-base-200/40 rounded-lg p-6">
        <div class="py-12 text-center">
          <svg class="w-12 h-12 mx-auto text-base-content/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10zM12 11v2m0 4h.01" />
          </svg>

          <h3 class="mt-4 text-lg font-medium">No API keys</h3>
          <p class="mt-2 text-base-content/70">Get started by creating your first API key.</p>
          <button
            @click="showCreateApiKeyModal = true"
            class="btn btn-primary mt-4"
          >
            Generate API key
          </button>
        </div>
      </div>
    </div>

    <!-- Create API Key Modal -->
    <div v-if="showCreateApiKeyModal" class="modal modal-open">
      <div class="modal-box max-w-2xl">
        <h3 class="text-lg font-bold mb-4">Generate new API key</h3>

        <div class="form-control mb-4">
          <label class="label">
            <span class="label-text">Key name</span>
          </label>
          <input
            v-model="newApiKeyName"
            type="text"
            placeholder="e.g., Production API, Development Key"
            class="input input-bordered w-full"
          >
          <label class="label">
            <span class="label-text-alt text-xs">Choose a descriptive name to identify this key.</span>
          </label>
        </div>

        <!-- <div class="form-control mb-4">
          <label class="label">
            <span class="label-text">Description (optional)</span>
          </label>
          <textarea
            v-model="newApiKeyDescription"
            placeholder="e.g., Used for automated domain management"
            class="textarea textarea-bordered w-full"
            rows="2"
          ></textarea>
        </div> -->

        <!-- Scope Selection -->
        <div class="form-control mb-6">
          <label class="label">
            <span class="label-text">Permissions</span>
          </label>
          
          <!-- Preset Options -->
          <div class="grid grid-cols-1 sm:grid-cols-3 gap-3 mb-4">
            <div 
              v-for="(preset, key) in availablePresets" 
              :key="key"
              class="border rounded-lg p-4 cursor-pointer transition-all"
              :class="selectedPreset === key ? 'border-primary bg-primary/10' : 'border-base-300 hover:border-base-400'"
              @click="selectPreset(key)"
            >
              <div class="flex items-center justify-between mb-2">
                <span class="font-medium">{{ getPresetDisplayName(key) }}</span>
                <input 
                  type="radio" 
                  :checked="selectedPreset === key"
                  class="radio radio-primary radio-sm"
                  readonly
                >
              </div>
              <p class="text-sm text-base-content/70 mb-2">{{ preset.description }}</p>
              <div class="flex flex-wrap gap-1">
                <span 
                  v-for="scope in preset.scopes" 
                  :key="scope"
                  class="badge badge-outline badge-xs"
                  :class="getScopeBadgeClass(scope)"
                >
                  {{ scope }}
                </span>
              </div>
            </div>
          </div>

          <!-- Custom Scopes (Advanced) -->
          <div class="collapse collapse-arrow border border-base-300 rounded-lg">
            <input 
              type="checkbox" 
              v-model="showCustomScopes"
              @change="onCustomScopesToggle"
            > 
            <div class="collapse-title text-sm font-medium">
              Advanced: Custom scope selection
            </div>
            <div class="collapse-content">
              <div class="space-y-3">
                <div v-for="(description, scope) in availableScopes" :key="scope">
                  <label class="flex items-center space-x-3 cursor-pointer">
                    <input 
                      type="checkbox" 
                      :value="scope"
                      v-model="selectedCustomScopes"
                      class="checkbox checkbox-sm"
                    >
                    <div class="flex-1">
                      <div class="flex items-center space-x-2">
                        <span class="text-sm font-medium">{{ scope }}</span>
                        <span 
                          class="badge badge-outline badge-xs"
                          :class="getScopeBadgeClass(scope)"
                        >
                          {{ scope }}
                        </span>
                      </div>
                      <p class="text-xs text-base-content/60">{{ description }}</p>
                    </div>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-action">
          <button
            @click="showCreateApiKeyModal = false"
            class="btn btn-ghost"
          >
            Cancel
          </button>
          <button
            @click="generateApiKey"
            :disabled="!canGenerateKey || generatingKey"
            class="btn btn-primary"
          >
            {{ generatingKey ? 'Generating...' : 'Generate key' }}
          </button>
        </div>
      </div>
    </div>

    <!-- API Key Generated Modal -->
    <div v-if="showApiKeyModal" class="modal modal-open">
      <div class="modal-box max-w-lg">
        <div class="flex items-center mb-4">
          <svg class="w-6 h-6 mr-2 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 class="text-lg font-bold">API key generated</h3>
        </div>

        <div class="alert bg-base-200/70 rounded-lg mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" /></svg>
          <div>
            <h4 class="font-bold">Important: save this key now!</h4>
            <div class="text-sm">This is the only time you'll see the full API key. Store it securely.</div>
          </div>
        </div>

        <div class="form-control mb-4">
          <label class="label">
            <span class="label-text">Your API key</span>
          </label>
          <div class="join w-full">
            <input
              :value="generatedApiKey"
              readonly
              class="input input-bordered join-item flex-1 font-mono text-sm bg-neutral text-neutral-content cursor-not-allowed"
            >
            <button
              @click="copyApiKey"
              class="btn btn-outline join-item"
            >
              {{ copied ? 'Copied!' : 'Copy' }}
            </button>
          </div>
        </div>

        <div class="modal-action">
          <button
            @click="closeApiKeyModal"
            class="btn btn-primary"
          >
            Done
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'

// Types
interface ApiKey {
  id: string
  name: string
  keyPrefix: string
  createdAt: string
  lastUsedAt: string | null
  scopes: string[]
  description?: string
}

interface ScopePreset {
  scopes: string[]
  description: string
}

// State
const apiKeys = ref<ApiKey[]>([])
const showCreateApiKeyModal = ref(false)
const showApiKeyModal = ref(false)
const newApiKeyName = ref('')
const newApiKeyDescription = ref('')
const generatingKey = ref(false)
const generatedApiKey = ref('')
const copied = ref(false)

// Scope management
const availableScopes = ref<Record<string, string>>({})
const availablePresets = ref<Record<string, ScopePreset>>({})
const selectedPreset = ref<string>('full-access')
const showCustomScopes = ref(false)
const selectedCustomScopes = ref<string[]>([])

// Computed
const canGenerateKey = computed(() => {
  if (!newApiKeyName.value.trim()) return false
  
  if (showCustomScopes.value) {
    return selectedCustomScopes.value.length > 0
  }
  
  return selectedPreset.value && availablePresets.value[selectedPreset.value]
})

const currentScopes = computed(() => {
  if (showCustomScopes.value) {
    return selectedCustomScopes.value
  }
  
  return availablePresets.value[selectedPreset.value]?.scopes || []
})

// Methods
const loadApiKeys = async () => {
  try {
    const response = await fetch('/api/api-keys', {
      credentials: 'include'
    })

    if (response.ok) {
      const data = await response.json()
      apiKeys.value = data.apiKeys || []
    } else {
      console.error('Failed to load API keys')
    }
  } catch (error) {
    console.error('Error loading API keys:', error)
  }
}

const loadAvailableScopes = async () => {
  try {
    const response = await fetch('/api/api-keys/scopes', {
      credentials: 'include'
    })

    if (response.ok) {
      const data = await response.json()
      availableScopes.value = data.scopes || {}
      availablePresets.value = data.presets || {}
    } else {
      console.error('Failed to load available scopes')
    }
  } catch (error) {
    console.error('Error loading available scopes:', error)
  }
}

const selectPreset = (presetKey: string) => {
  selectedPreset.value = presetKey
  showCustomScopes.value = false
}

const onCustomScopesToggle = () => {
  if (showCustomScopes.value) {
    selectedPreset.value = ''
    selectedCustomScopes.value = []
  } else {
    selectedPreset.value = 'full-access'
  }
}

const generateApiKey = async () => {
  if (!canGenerateKey.value) return

  generatingKey.value = true
  try {
    const response = await fetch('/api/api-keys', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify({
        name: newApiKeyName.value.trim(),
        description: newApiKeyDescription.value.trim() || undefined,
        scopes: currentScopes.value
      })
    })

    if (response.ok) {
      const data = await response.json()
      generatedApiKey.value = data.apiKey.key
      showCreateApiKeyModal.value = false
      showApiKeyModal.value = true
      
      // Reset form
      newApiKeyName.value = ''
      newApiKeyDescription.value = ''
      selectedPreset.value = 'full-access'
      showCustomScopes.value = false
      selectedCustomScopes.value = []

      // Reload API keys list
      await loadApiKeys()
    } else {
      const error = await response.json()
      alert(`Failed to generate API key: ${error.message}`)
    }
  } catch (error) {
    console.error('Error generating API key:', error)
    alert('Failed to generate API key. Please try again.')
  } finally {
    generatingKey.value = false
  }
}

const revokeApiKey = async (apiKey: ApiKey) => {
  if (!confirm(`Are you sure you want to revoke "${apiKey.name}"? This action cannot be undone.`)) {
    return
  }

  try {
    const response = await fetch(`/api/api-keys/${apiKey.id}`, {
      method: 'DELETE',
      credentials: 'include'
    })

    if (response.ok) {
      // Remove from local list
      apiKeys.value = apiKeys.value.filter(k => k.id !== apiKey.id)
    } else {
      const error = await response.json()
      alert(`Failed to revoke API key: ${error.message}`)
    }
  } catch (error) {
    console.error('Error revoking API key:', error)
    alert('Failed to revoke API key. Please try again.')
  }
}

const copyApiKey = async () => {
  try {
    await navigator.clipboard.writeText(generatedApiKey.value)
    copied.value = true
    setTimeout(() => {
      copied.value = false
    }, 2000)
  } catch (error) {
    console.error('Failed to copy API key:', error)
    // Fallback for older browsers
    const textArea = document.createElement('textarea')
    textArea.value = generatedApiKey.value
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    copied.value = true
    setTimeout(() => {
      copied.value = false
    }, 2000)
  }
}

const closeApiKeyModal = () => {
  showApiKeyModal.value = false
  generatedApiKey.value = ''
  copied.value = false
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const getScopeBadgeClass = (scope: string) => {
  if (scope === '*') return 'badge-error'
  if (scope.endsWith(':*')) return 'badge-warning'
  if (scope.endsWith(':read') || scope.endsWith(':status')) return 'badge-info'
  if (scope.endsWith(':write') || scope.endsWith(':config')) return 'badge-success'
  return 'badge-ghost'
}

const getPresetDisplayName = (key: string) => {
  const names: Record<string, string> = {
    'full-access': 'Full Access',
    'read-only': 'Read Only',
    'api-user': 'API User (Limited)'
  }
  return names[key] || key
}

onMounted(async () => {
  await Promise.all([
    loadApiKeys(),
    loadAvailableScopes()
  ])
})
</script>
