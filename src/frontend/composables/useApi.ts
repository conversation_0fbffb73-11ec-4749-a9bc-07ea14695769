import { ref, readonly, type Ref } from 'vue'
import type { ApiResponse, ApiError } from '@types'

export function useApi() {
  const loading: Ref<boolean> = ref(false)
  const error: Ref<string | null> = ref(null)

  async function request<T>(
    url: string,
    options: RequestInit = {}
  ): Promise<T> {
    loading.value = true
    error.value = null

    try {
      // Only set Content-Type for requests with a body
      const headers: Record<string, string> = {
        ...options.headers as Record<string, string>
      }

      if (options.body) {
        headers['Content-Type'] = 'application/json'
      }

      const response = await fetch(url, {
        headers,
        credentials: 'include', // Include cookies for authentication
        ...options
      })

      if (!response.ok) {
        const errorData: ApiError = await response.json()
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      return data as T
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      error.value = errorMessage
      throw new Error(errorMessage)
    } finally {
      loading.value = false
    }
  }

  const get = <T>(url: string, options?: RequestInit) =>
    request<T>(url, { method: 'GET', ...options })

  const post = <T>(url: string, data: any, options?: RequestInit) =>
    request<T>(url, {
      method: 'POST',
      body: JSON.stringify(data),
      ...options
    })

  const put = <T>(url: string, data: any, options?: RequestInit) =>
    request<T>(url, {
      method: 'PUT',
      body: JSON.stringify(data),
      ...options
    })

  const patch = <T>(url: string, data?: any, options?: RequestInit) =>
    request<T>(url, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
      ...options
    })

  const del = <T>(url: string, options?: RequestInit) =>
    request<T>(url, { method: 'DELETE', ...options })

  return {
    loading: readonly(loading),
    error: readonly(error),
    request,
    get,
    post,
    put,
    patch,
    del
  }
}

// Specific API composables
export function useDomainApi() {
  const { loading, error, get, post, put, del } = useApi()

  const getDomains = () => get('/api/domains')
  const createDomain = (data: any) => post('/api/domains', data)
  const updateDomain = (id: string, data: any) => put(`/api/domains/${id}`, data)
  const deleteDomain = (domainId: string) => del(`/api/domains/${domainId}`)
  const verifyDomain = (domainId: string) => post(`/api/domains/${domainId}/verify`, {})

  return {
    loading,
    error,
    getDomains,
    createDomain,
    updateDomain,
    deleteDomain,
    verifyDomain
  }
}

export function useWebhookApi() {
  const { loading, error, get, post, put, del } = useApi()

  const getWebhooks = () => get('/api/webhooks')
  const createWebhook = (data: any) => post('/api/webhooks', data)
  const updateWebhook = (id: string, data: any) => put(`/api/webhooks/${id}`, data)
  const deleteWebhook = (id: string) => del(`/api/webhooks/${id}`)
  const verifyWebhook = (id: string) => post(`/api/webhooks/${id}/verify`, {})
  const completeWebhookVerification = (id: string, token: string) =>
    post(`/api/webhooks/${id}/verify/complete`, { verificationToken: token })
  const testWebhook = (id: string) => post(`/api/webhooks/${id}/test`, {})
  const testWebhookCustom = (id: string, customPayload?: { subject: string; content: { text: string; html: string } }) =>
    post(`/api/webhooks/${id}/test`, customPayload ? { customPayload } : {})

  return {
    loading,
    error,
    getWebhooks,
    createWebhook,
    updateWebhook,
    deleteWebhook,
    verifyWebhook,
    completeWebhookVerification,
    testWebhook,
    testWebhookCustom
  }
}

export function useAliasApi() {
  const { loading, error, get, post, put, del } = useApi()

  const getAliases = () => get('/api/aliases')
  const createAlias = (data: any) => post('/api/aliases', data)
  const updateAlias = (id: string, data: any) => put(`/api/aliases/${id}`, data)
  const deleteAlias = (id: string) => del(`/api/aliases/${id}`)

  return {
    loading,
    error,
    getAliases,
    createAlias,
    updateAlias,
    deleteAlias
  }
}