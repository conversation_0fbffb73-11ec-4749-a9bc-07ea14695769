package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/exec"
	"strings"
	"time"

	"github.com/gorilla/mux"
	_ "github.com/mattn/go-sqlite3"
)

const (
	MAIN_CF_FILE        = "/etc/postfix/main.cf"
	ALIASES_FILE        = "/etc/aliases"
	PORT                = "3001"
	SQLITE_DB_PATH      = "/opt/eu-email-webhook/data/postfix.db"
	VIRTUAL_DOMAINS_CF  = "/opt/eu-email-webhook/data/virtual_domains.cf"
	VIRTUAL_ALIASES_CF  = "/opt/eu-email-webhook/data/virtual_aliases.cf"
	TRANSPORT_CF        = "/opt/eu-email-webhook/data/transport.cf"
)

type PostfixManager struct {
	db *sql.DB
}

type DomainRequest struct {
	Domain string `json:"domain"`
}

type SpamFilterRequest struct {
	Enabled bool `json:"enabled"`
}

type DomainResponse struct {
	Success   bool     `json:"success"`
	Message   string   `json:"message"`
	Domain    string   `json:"domain,omitempty"`
	Error     string   `json:"error,omitempty"`
	Timestamp string   `json:"timestamp"`
}

type StatusResponse struct {
	Service           string   `json:"service"`
	Status            string   `json:"status"`
	ConfiguredDomains []string `json:"configured_domains"`
	TotalDomains      int      `json:"total_domains"`
	ConfigValid       bool     `json:"config_valid"`
	SQLiteEnabled     bool     `json:"sqlite_enabled"`
	DatabasePath      string   `json:"database_path"`
	Timestamp         string   `json:"timestamp"`
	Error             string   `json:"error,omitempty"`
}

func main() {
	// Security check - must run as root
	if os.Geteuid() != 0 {
		log.Fatal("This service must run as root to manage Postfix configuration")
	}

	pm := &PostfixManager{}
	
	// Initialize SQLite database
	if err := pm.initializeDatabase(); err != nil {
		log.Fatalf("Failed to initialize SQLite database: %v", err)
	}
	defer pm.db.Close()

	// Initialize Postfix configuration on startup
	if err := pm.setupInitialConfig(); err != nil {
		log.Printf("Warning: Failed to setup initial config: %v", err)
	}

	r := mux.NewRouter()
	
	// CORS middleware
	r.Use(corsMiddleware)
	
	// Routes
	r.HandleFunc("/health", pm.healthHandler).Methods("GET")
	r.HandleFunc("/status", pm.statusHandler).Methods("GET")
	r.HandleFunc("/domains", pm.addDomainHandler).Methods("POST")
	r.HandleFunc("/domains/{domain}/aliases", pm.addAliasHandler).Methods("POST")
	r.HandleFunc("/domains/{domain}/spam-filter", pm.updateSpamFilterHandler).Methods("PUT")
	r.HandleFunc("/domains/{domain}", pm.removeDomainHandler).Methods("DELETE")
	r.HandleFunc("/domains", pm.listDomainsHandler).Methods("GET")
	r.HandleFunc("/setup", pm.setupHandler).Methods("POST")

	log.Printf("🔐 Postfix Manager Service (SQLite) starting on port %s", PORT)
	log.Printf("📀 SQLite database: %s", SQLITE_DB_PATH)
	log.Printf("⚠️  Running with root privileges for Postfix management")
	
	if err := http.ListenAndServe(":"+PORT, r); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}

func corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type")
		
		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}
		
		next.ServeHTTP(w, r)
	})
}

func (pm *PostfixManager) initializeDatabase() error {
	// Ensure data directory exists
	dataDir := "/opt/eu-email-webhook/data"
	if err := os.MkdirAll(dataDir, 0755); err != nil {
		return fmt.Errorf("failed to create data directory: %v", err)
	}

	// Open SQLite database
	var err error
	pm.db, err = sql.Open("sqlite3", SQLITE_DB_PATH)
	if err != nil {
		return fmt.Errorf("failed to open SQLite database: %v", err)
	}

	// Test connection
	if err := pm.db.Ping(); err != nil {
		return fmt.Errorf("failed to ping SQLite database: %v", err)
	}

	// Create tables
	schema := `
	CREATE TABLE IF NOT EXISTS virtual_domains (
		domain VARCHAR(255) PRIMARY KEY,
		destination VARCHAR(255) NOT NULL DEFAULT 'process-email',
		active BOOLEAN DEFAULT 1,
		spam_filtering BOOLEAN DEFAULT 0,
		spam_threshold_green REAL DEFAULT 2.0,
		spam_threshold_red REAL DEFAULT 5.0,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	);

	CREATE INDEX IF NOT EXISTS idx_virtual_domains_active ON virtual_domains(domain, active);
	CREATE INDEX IF NOT EXISTS idx_virtual_domains_spam_filtering ON virtual_domains(domain, spam_filtering, active);

	CREATE TABLE IF NOT EXISTS virtual_aliases (
		email VARCHAR(255) PRIMARY KEY,
		destination VARCHAR(255) NOT NULL,
		domain VARCHAR(255),
		active BOOLEAN DEFAULT 1,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		FOREIGN KEY (domain) REFERENCES virtual_domains(domain) ON DELETE CASCADE
	);

	CREATE INDEX IF NOT EXISTS idx_virtual_aliases_active ON virtual_aliases(email, active);
	CREATE INDEX IF NOT EXISTS idx_virtual_aliases_domain ON virtual_aliases(domain);

	CREATE TRIGGER IF NOT EXISTS update_virtual_domains_timestamp 
		AFTER UPDATE ON virtual_domains
	BEGIN
		UPDATE virtual_domains 
		SET updated_at = CURRENT_TIMESTAMP 
		WHERE domain = NEW.domain;
	END;

	CREATE TRIGGER IF NOT EXISTS update_virtual_aliases_timestamp 
		AFTER UPDATE ON virtual_aliases
	BEGIN
		UPDATE virtual_aliases 
		SET updated_at = CURRENT_TIMESTAMP 
		WHERE email = NEW.email;
	END;
	`

	if _, err := pm.db.Exec(schema); err != nil {
		return fmt.Errorf("failed to create database schema: %v", err)
	}

	log.Println("✅ SQLite database initialized successfully")
	return nil
}

func (pm *PostfixManager) healthHandler(w http.ResponseWriter, r *http.Request) {
	// Enhanced health check - verify SQLite and Postfix
	health := map[string]interface{}{
		"status":         "ok",
		"service":        "postfix-manager",
		"sqlite_enabled": true,
		"timestamp":     time.Now().Format(time.RFC3339),
	}

	// Check SQLite connection
	if err := pm.db.Ping(); err != nil {
		health["status"] = "error"
		health["sqlite_error"] = err.Error()
	}

	// Check Postfix configuration
	if !pm.testConfiguration() {
		health["status"] = "warning"
		health["postfix_config"] = "invalid"
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(health)
}

func (pm *PostfixManager) statusHandler(w http.ResponseWriter, r *http.Request) {
	domains, err := pm.getConfiguredDomains()
	if err != nil {
		log.Printf("Error getting configured domains: %v", err)
		domains = []string{}
	}
	
	configValid := pm.testConfiguration()
	
	status := "healthy"
	if !configValid {
		status = "error"
	}
	
	response := StatusResponse{
		Service:           "postfix-manager",
		Status:            status,
		ConfiguredDomains: domains,
		TotalDomains:      len(domains),
		ConfigValid:       configValid,
		SQLiteEnabled:     true,
		DatabasePath:      SQLITE_DB_PATH,
		Timestamp:         time.Now().Format(time.RFC3339),
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (pm *PostfixManager) addDomainHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("🔍 addDomainHandler called")
	
	var req DomainRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		log.Printf("🔍 JSON decode error: %v", err)
		pm.sendError(w, "Invalid JSON", http.StatusBadRequest)
		return
	}
	
	if req.Domain == "" {
		log.Printf("🔍 Empty domain provided")
		pm.sendError(w, "Domain is required", http.StatusBadRequest)
		return
	}
	
	log.Printf("🔍 Request to add domain: %s", req.Domain)
	
	if err := pm.addDomain(req.Domain); err != nil {
		log.Printf("🔍 addDomain failed for %s: %v", req.Domain, err)
		pm.sendError(w, fmt.Sprintf("Failed to add domain: %v", err), http.StatusInternalServerError)
		return
	}
	
	log.Printf("🔍 addDomain succeeded for %s, sending success response", req.Domain)
	
	response := DomainResponse{
		Success:   true,
		Message:   "Domain added successfully to SQLite backend",
		Domain:    req.Domain,
		Timestamp: time.Now().Format(time.RFC3339),
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
	
	log.Printf("🔍 Success response sent for domain: %s", req.Domain)
}

func (pm *PostfixManager) addAliasHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	domain := vars["domain"]
	
	if domain == "" {
		pm.sendError(w, "Domain is required", http.StatusBadRequest)
		return
	}
	
	type AliasRequest struct {
		Alias       string `json:"alias"`
		Destination string `json:"destination"`
	}
	
	var req AliasRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		pm.sendError(w, "Invalid JSON", http.StatusBadRequest)
		return
	}
	
	if req.Alias == "" || req.Destination == "" {
		pm.sendError(w, "Alias and destination are required", http.StatusBadRequest)
		return
	}
	
	// Validate that the alias belongs to the domain
	if !strings.HasSuffix(req.Alias, "@"+domain) {
		pm.sendError(w, "Alias must belong to the specified domain", http.StatusBadRequest)
		return
	}
	
	log.Printf("Adding specific alias: %s → %s", req.Alias, req.Destination)
	
	if err := pm.addSpecificAlias(req.Alias, req.Destination, domain); err != nil {
		pm.sendError(w, fmt.Sprintf("Failed to add alias: %v", err), http.StatusInternalServerError)
		return
	}
	
	response := map[string]interface{}{
		"success":     true,
		"message":     "Alias added successfully",
		"alias":       req.Alias,
		"destination": req.Destination,
		"domain":      domain,
		"timestamp":   time.Now().Format(time.RFC3339),
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (pm *PostfixManager) removeDomainHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	domain := vars["domain"]
	
	if domain == "" {
		pm.sendError(w, "Domain is required", http.StatusBadRequest)
		return
	}
	
	log.Printf("Removing domain from SQLite: %s", domain)
	
	if err := pm.removeDomain(domain); err != nil {
		log.Printf("Failed to remove domain %s: %v", domain, err)
		pm.sendError(w, fmt.Sprintf("Failed to remove domain: %v", err), http.StatusInternalServerError)
		return
	}
	
	response := DomainResponse{
		Success:   true,
		Message:   "Domain removed successfully from SQLite backend",
		Domain:    domain,
		Timestamp: time.Now().Format(time.RFC3339),
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (pm *PostfixManager) listDomainsHandler(w http.ResponseWriter, r *http.Request) {
	domains, err := pm.getConfiguredDomains()
	if err != nil {
		log.Printf("Error getting domains from SQLite: %v", err)
		domains = []string{}
	}
	
	response := map[string]interface{}{
		"domains":    domains,
		"total":      len(domains),
		"backend":    "sqlite",
		"timestamp":  time.Now().Format(time.RFC3339),
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (pm *PostfixManager) setupHandler(w http.ResponseWriter, r *http.Request) {
	log.Println("Setting up initial Postfix configuration with SQLite")
	
	if err := pm.setupInitialConfig(); err != nil {
		log.Printf("Setup failed: %v", err)
		pm.sendError(w, fmt.Sprintf("Setup failed: %v", err), http.StatusInternalServerError)
		return
	}
	
	response := DomainResponse{
		Success:   true,
		Message:   "Initial configuration completed with SQLite backend",
		Timestamp: time.Now().Format(time.RFC3339),
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (pm *PostfixManager) sendError(w http.ResponseWriter, message string, statusCode int) {
	response := DomainResponse{
		Success:   false,
		Error:     message,
		Timestamp: time.Now().Format(time.RFC3339),
	}
	
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(response)
}

// Core SQLite-based Postfix management functions

func (pm *PostfixManager) addDomain(domain string) error {
	log.Printf("🔍 addDomain called with domain: %s", domain)
	
	// 1. Add domain to SQLite database
	log.Printf("🔍 Step 1: Adding domain %s to SQLite...", domain)
	if err := pm.addDomainToSQLite(domain); err != nil {
		log.Printf("🔍 Step 1 failed for domain %s: %v", domain, err)
		return fmt.Errorf("failed to add domain to SQLite: %v", err)
	}
	log.Printf("🔍 Step 1 completed for domain %s", domain)
	
	// 2. Add catch-all alias automatically (NEW!)
	log.Printf("🔍 Step 2: Adding catch-all alias for domain %s...", domain)
	if err := pm.addCatchAllAlias(domain); err != nil {
		log.Printf("🔍 Step 2 warning for domain %s: %v", domain, err)
		// Don't fail the entire operation for alias creation - it's not critical
		log.Printf("⚠️ Continuing without catch-all alias for domain %s", domain)
	} else {
		log.Printf("🔍 Step 2 completed for domain %s", domain)
	}
	
	// 3. Update main.cf if needed
	log.Printf("🔍 Step 3: Updating main.cf for domain %s...", domain)
	if err := pm.updateMainConfigForSQLite(); err != nil {
		log.Printf("🔍 Step 3 failed for domain %s: %v", domain, err)
		return fmt.Errorf("failed to update main config for SQLite: %v", err)
	}
	log.Printf("🔍 Step 3 completed for domain %s", domain)
	
	// 4. Reload Postfix
	log.Printf("🔍 Step 4: Reloading Postfix for domain %s...", domain)
	if err := pm.reloadPostfix(); err != nil {
		log.Printf("🔍 Step 4 failed for domain %s: %v", domain, err)
		return fmt.Errorf("failed to reload postfix: %v", err)
	}
	log.Printf("🔍 Step 4 completed for domain %s", domain)
	
	log.Printf("✅ Domain %s added successfully to SQLite backend with catch-all alias", domain)
	return nil
}

// New function to add catch-all alias automatically
func (pm *PostfixManager) addCatchAllAlias(domain string) error {
	log.Printf("🔍 Adding catch-all alias for domain: %s", domain)
	
	catchAllEmail := fmt.Sprintf("@%s", domain)
	
	// Check if catch-all already exists
	var existingAlias string
	err := pm.db.QueryRow("SELECT email FROM virtual_aliases WHERE email = ?", catchAllEmail).Scan(&existingAlias)
	if err == nil {
		log.Printf("🔍 Catch-all alias already exists for domain %s", domain)
		return nil
	} else if err != sql.ErrNoRows {
		return fmt.Errorf("failed to check existing catch-all alias: %v", err)
	}
	
	log.Printf("🔍 Creating catch-all alias: %s → process-email", catchAllEmail)
	
	// Insert catch-all alias
	_, err = pm.db.Exec(`
		INSERT INTO virtual_aliases (email, destination, domain, active) 
		VALUES (?, 'process-email', ?, 1)
	`, catchAllEmail, domain)
	
	if err != nil {
		return fmt.Errorf("failed to insert catch-all alias: %v", err)
	}
	
	log.Printf("✅ Catch-all alias %s → process-email created successfully", catchAllEmail)
	return nil
}

func (pm *PostfixManager) addSpecificAlias(email, destination, domain string) error {
	log.Printf("🔍 Adding specific alias: %s → %s", email, destination)
	
	// Check if alias already exists
	var existingAlias string
	err := pm.db.QueryRow("SELECT email FROM virtual_aliases WHERE email = ?", email).Scan(&existingAlias)
	if err == nil {
		return fmt.Errorf("alias %s already exists", email)
	} else if err != sql.ErrNoRows {
		return fmt.Errorf("failed to check existing alias: %v", err)
	}
	
	// Insert specific alias
	_, err = pm.db.Exec(`
		INSERT INTO virtual_aliases (email, destination, domain, active) 
		VALUES (?, ?, ?, 1)
	`, email, destination, domain)
	
	if err != nil {
		return fmt.Errorf("failed to insert specific alias: %v", err)
	}
	
	log.Printf("✅ Specific alias %s → %s created successfully", email, destination)
	return nil
}

func (pm *PostfixManager) removeDomain(domain string) error {
	log.Printf("🔍 Removing domain and associated aliases: %s", domain)
	
	// 1. Remove domain from SQLite database (CASCADE will handle aliases)
	if err := pm.removeDomainFromSQLite(domain); err != nil {
		return fmt.Errorf("failed to remove domain from SQLite: %v", err)
	}
	
	// 2. Explicitly remove any remaining aliases for this domain
	if err := pm.removeAliasesForDomain(domain); err != nil {
		log.Printf("Warning: Failed to clean up aliases for domain %s: %v", domain, err)
		// Don't fail the entire operation
	}
	
	// 3. Reload Postfix
	if err := pm.reloadPostfix(); err != nil {
		return fmt.Errorf("failed to reload postfix: %v", err)
	}
	
	log.Printf("✅ Domain %s and associated aliases removed successfully", domain)
	return nil
}

func (pm *PostfixManager) removeAliasesForDomain(domain string) error {
	log.Printf("🔍 Removing aliases for domain: %s", domain)
	
	result, err := pm.db.Exec("DELETE FROM virtual_aliases WHERE domain = ?", domain)
	if err != nil {
		return fmt.Errorf("failed to delete aliases for domain: %v", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get affected alias rows: %v", err)
	}
	
	log.Printf("✅ Removed %d aliases for domain %s", rowsAffected, domain)
	return nil
}

func (pm *PostfixManager) addDomainToSQLite(domain string) error {
	log.Printf("🔍 addDomainToSQLite called with domain: %s", domain)
	
	// Check if domain already exists
	var existingDomain string
	log.Printf("🔍 Checking if domain %s already exists...", domain)
	err := pm.db.QueryRow("SELECT domain FROM virtual_domains WHERE domain = ?", domain).Scan(&existingDomain)
	if err == nil {
		// Domain exists
		log.Printf("🔍 Domain %s already exists, returning", domain)
		return nil
	} else if err != sql.ErrNoRows {
		// Real error occurred
		log.Printf("🔍 Error checking domain %s: %v", domain, err)
		return fmt.Errorf("failed to check existing domain: %v", err)
	}
	
	log.Printf("🔍 Domain %s doesn't exist, inserting...", domain)
	
	// Insert new domain
	_, err = pm.db.Exec(`
		INSERT INTO virtual_domains (domain, destination, active) 
		VALUES (?, 'process-email', 1)
	`, domain)
	
	if err != nil {
		log.Printf("🔍 Failed to insert domain %s: %v", domain, err)
		return fmt.Errorf("failed to insert domain into SQLite: %v", err)
	}
	
	log.Printf("✅ Domain %s successfully inserted into SQLite database", domain)
	return nil
}

func (pm *PostfixManager) removeDomainFromSQLite(domain string) error {
	// Remove domain and associated aliases (CASCADE will handle aliases)
	result, err := pm.db.Exec("DELETE FROM virtual_domains WHERE domain = ?", domain)
	if err != nil {
		return fmt.Errorf("failed to delete domain from SQLite: %v", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %v", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("domain %s not found in SQLite database", domain)
	}
	
	log.Printf("Domain %s removed from SQLite database", domain)
	return nil
}

func (pm *PostfixManager) getConfiguredDomains() ([]string, error) {
	rows, err := pm.db.Query("SELECT domain FROM virtual_domains WHERE active = 1 ORDER BY domain")
	if err != nil {
		return nil, fmt.Errorf("failed to query domains from SQLite: %v", err)
	}
	defer rows.Close()
	
	var domains []string
	for rows.Next() {
		var domain string
		if err := rows.Scan(&domain); err != nil {
			log.Printf("Error scanning domain row: %v", err)
			continue
		}
		domains = append(domains, domain)
	}
	
	return domains, nil
}

func (pm *PostfixManager) updateMainConfigForSQLite() error {
	if _, err := os.Stat("/.dockerenv"); err == nil {
		log.Println("🐳 Container environment - would update host /etc/postfix/main.cf")
		log.Printf("📝 Would add: virtual_alias_domains = sqlite:%s", VIRTUAL_DOMAINS_CF)
		log.Printf("📝 Would add: virtual_alias_maps = sqlite:%s", VIRTUAL_ALIASES_CF)
		return nil
	}
	
	// Check if main.cf already configured for SQLite
	data, err := os.ReadFile(MAIN_CF_FILE)
	if err != nil {
		return fmt.Errorf("failed to read main.cf: %v", err)
	}
	
	content := string(data)
	
	// Check if SQLite configuration already exists
	if strings.Contains(content, "sqlite:"+VIRTUAL_DOMAINS_CF) {
		log.Println("main.cf already configured for SQLite")
		return nil
	}
	
	// Add SQLite configuration
	sqliteConfig := fmt.Sprintf(`
# SQLite virtual domain configuration
virtual_alias_domains = sqlite:%s
virtual_alias_maps = sqlite:%s
`, VIRTUAL_DOMAINS_CF, VIRTUAL_ALIASES_CF)
	
	// Remove old file-based configuration if it exists
	lines := strings.Split(content, "\n")
	var filteredLines []string
	
	for _, line := range lines {
		// Skip old virtual file configurations
		if strings.HasPrefix(line, "virtual_alias_domains") && strings.Contains(line, "hash:") {
			continue
		}
		if strings.HasPrefix(line, "virtual_alias_maps") && strings.Contains(line, "hash:") {
			continue
		}
		filteredLines = append(filteredLines, line)
	}
	
	// Add SQLite configuration
	newContent := strings.Join(filteredLines, "\n") + sqliteConfig
	
	if err := os.WriteFile(MAIN_CF_FILE, []byte(newContent), 0644); err != nil {
		return fmt.Errorf("failed to write main.cf: %v", err)
	}
	
	log.Println("✅ main.cf updated for SQLite backend")
	return nil
}

func (pm *PostfixManager) testConfiguration() bool {
	// In container environment, assume config is valid
	if _, err := os.Stat("/.dockerenv"); err == nil {
		return true
	}
	
	cmd := exec.Command("postfix", "check")
	return cmd.Run() == nil
}

func (pm *PostfixManager) reloadPostfix() error {
	// Check if we're in a container environment
	if _, err := os.Stat("/.dockerenv"); err == nil {
		log.Println("🐳 Container environment - skipping host Postfix reload")
		return nil
	}
	
	// On host system, try systemctl first
	cmd := exec.Command("systemctl", "reload", "postfix")
	if err := cmd.Run(); err != nil {
		// Try postfix reload as fallback
		cmd = exec.Command("postfix", "reload")
		if err := cmd.Run(); err != nil {
			return fmt.Errorf("failed to reload postfix: %v", err)
		}
	}
	log.Println("✅ Postfix reloaded successfully")
	return nil
}

func (pm *PostfixManager) setupInitialConfig() error {
	// Create SQLite query configuration files
	if err := pm.createSQLiteQueryFiles(); err != nil {
		return fmt.Errorf("failed to create SQLite query files: %v", err)
	}
	
	// Update main.cf for SQLite
	if err := pm.updateMainConfigForSQLite(); err != nil {
		return fmt.Errorf("failed to update main.cf for SQLite: %v", err)
	}
	
	// Setup aliases
	if err := pm.setupAliases(); err != nil {
		return fmt.Errorf("failed to setup aliases: %v", err)
	}
	
	log.Println("✅ Initial Postfix configuration completed with SQLite backend")
	return nil
}

func (pm *PostfixManager) createSQLiteQueryFiles() error {
	// Ensure config directory exists
	configDir := "/opt/eu-email-webhook/data"
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %v", err)
	}
	
	// Virtual domains query file
	domainsConfig := fmt.Sprintf(`query = SELECT destination FROM virtual_domains WHERE domain='%%s' AND active=1
dbpath = %s
`, SQLITE_DB_PATH)
	
	if err := os.WriteFile(VIRTUAL_DOMAINS_CF, []byte(domainsConfig), 0644); err != nil {
		return fmt.Errorf("failed to write virtual domains config: %v", err)
	}
	
	// Virtual aliases query file
	aliasesConfig := fmt.Sprintf(`query = SELECT destination FROM virtual_aliases WHERE email='%%s' AND active=1
dbpath = %s
`, SQLITE_DB_PATH)

	if err := os.WriteFile(VIRTUAL_ALIASES_CF, []byte(aliasesConfig), 0644); err != nil {
		return fmt.Errorf("failed to write virtual aliases config: %v", err)
	}

	// Transport map query file for spam filtering
	transportConfig := fmt.Sprintf(`query = SELECT CASE
    WHEN spam_filtering = 1 THEN 'amavis:[127.0.0.1]:10024'
    ELSE NULL
END FROM virtual_domains
WHERE domain='%%s' AND active=1
dbpath = %s
`, SQLITE_DB_PATH)

	if err := os.WriteFile(TRANSPORT_CF, []byte(transportConfig), 0644); err != nil {
		return fmt.Errorf("failed to write transport config: %v", err)
	}

	log.Println("✅ SQLite query configuration files created (domains, aliases, transport)")
	return nil
}

func (pm *PostfixManager) setupAliases() error {
	if _, err := os.Stat("/.dockerenv"); err == nil {
		log.Println("🐳 Container environment - skipping host aliases setup")
		log.Printf("📝 Would add alias: process-email: \"|%s\"", 
			os.Getenv("PROCESS_EMAIL_SCRIPT"))
		return nil
	}
	
	// Get process email script path from environment or use default
	scriptPath := os.Getenv("PROCESS_EMAIL_SCRIPT")
	if scriptPath == "" {
		scriptPath = "/opt/eu-email-webhook/scripts/production/process-email.js"
	}
	
	// Ensure process-email alias exists
	aliasEntry := fmt.Sprintf(`process-email: "|%s"`, scriptPath)
	
	aliasData, err := os.ReadFile(ALIASES_FILE)
	if err != nil {
		// Create aliases file
		if err := os.WriteFile(ALIASES_FILE, []byte(aliasEntry+"\n"), 0644); err != nil {
			return fmt.Errorf("failed to create aliases file: %v", err)
		}
	} else {
		// Check if alias exists
		if !strings.Contains(string(aliasData), "process-email:") {
			newContent := string(aliasData) + "\n" + aliasEntry + "\n"
			if err := os.WriteFile(ALIASES_FILE, []byte(newContent), 0644); err != nil {
				return fmt.Errorf("failed to update aliases file: %v", err)
			}
		}
	}
	
	// Run newaliases
	cmd := exec.Command("newaliases")
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("newaliases failed: %v", err)
	}
	
	log.Println("✅ Aliases configured successfully")
	return nil
}

// updateSpamFilterHandler handles PUT /domains/{domain}/spam-filter
func (pm *PostfixManager) updateSpamFilterHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	domain := vars["domain"]

	log.Printf("🛡️  Updating spam filter settings for domain: %s", domain)

	var req SpamFilterRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		pm.sendError(w, "Invalid JSON payload", http.StatusBadRequest)
		return
	}

	// Validate domain exists
	var exists bool
	err := pm.db.QueryRow("SELECT EXISTS(SELECT 1 FROM virtual_domains WHERE domain = ?)", domain).Scan(&exists)
	if err != nil {
		log.Printf("❌ Database error checking domain %s: %v", domain, err)
		pm.sendError(w, "Database error", http.StatusInternalServerError)
		return
	}

	if !exists {
		log.Printf("❌ Domain %s not found", domain)
		pm.sendError(w, "Domain not found", http.StatusNotFound)
		return
	}

	// Update spam filtering setting
	_, err = pm.db.Exec(`
		UPDATE virtual_domains
		SET spam_filtering = ?, updated_at = CURRENT_TIMESTAMP
		WHERE domain = ?
	`, req.Enabled, domain)

	if err != nil {
		log.Printf("❌ Failed to update spam filtering for domain %s: %v", domain, err)
		pm.sendError(w, "Failed to update spam filtering", http.StatusInternalServerError)
		return
	}

	// Reload Postfix configuration to apply transport map changes
	if err := pm.reloadPostfix(); err != nil {
		log.Printf("⚠️  Warning: Failed to reload Postfix after spam filter update: %v", err)
		// Don't fail the request - the database update succeeded
	}

	log.Printf("✅ Spam filtering %s for domain %s",
		map[bool]string{true: "enabled", false: "disabled"}[req.Enabled], domain)

	response := DomainResponse{
		Success:   true,
		Message:   fmt.Sprintf("Spam filtering %s for domain %s",
			map[bool]string{true: "enabled", false: "disabled"}[req.Enabled], domain),
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}
