# Migration Guide: Amavis to Alias-Based Spam Filtering

This document provides step-by-step instructions for migrating existing EU Email Webhook installations from the old Amavis-based spam filtering approach to the new alias-based approach.

## Overview

The new alias-based spam filtering approach offers several advantages:
- **Resource Efficiency**: Free users never touch SpamAssassin, saving server resources
- **Simplified Architecture**: No complex transport routing or Amavis integration
- **Plan-Based Routing**: Automatic routing based on user subscription level
- **Better Performance**: Direct SpamAssassin integration via spamc for Pro+ users

## Migration Methods

### Option 1: Automated Migration (Recommended)

Use the provided migration script for a fully automated migration:

```bash
cd /opt/eu-email-webhook
sudo ./deploy/migrate-to-alias-spam-filtering.sh
```

The script will:
1. Stop and disable <PERSON><PERSON>s
2. Update Postfix configuration
3. Add spam-process-email alias
4. Update database routing
5. Clean up old configuration

### Option 2: Manual Migration

Follow these steps if you prefer manual control or need to customize the migration:

## Manual Migration Steps

### Prerequisites

- Root or sudo access to the server
- Existing EU Email Webhook installation with Amavis
- Basic familiarity with Postfix and SQLite

### Step 1: Backup Current Configuration

```bash
# Backup Postfix configuration
sudo cp /etc/postfix/main.cf /etc/postfix/main.cf.backup.$(date +%Y%m%d_%H%M%S)
sudo cp /etc/postfix/master.cf /etc/postfix/master.cf.backup.$(date +%Y%m%d_%H%M%S)

# Backup SQLite database
sudo cp /opt/eu-email-webhook/data/postfix.db /opt/eu-email-webhook/data/postfix.db.backup.$(date +%Y%m%d_%H%M%S)

# Backup aliases
sudo cp /etc/aliases /etc/aliases.backup.$(date +%Y%m%d_%H%M%S)
```

### Step 2: Stop Amavis Service

```bash
# Stop Amavis service
sudo systemctl stop amavis

# Disable Amavis from starting on boot
sudo systemctl disable amavis

# Verify Amavis is stopped
sudo systemctl status amavis
```

### Step 3: Update Postfix Configuration

#### Remove Transport Maps from main.cf

```bash
# Edit main.cf and remove or comment out the transport_maps line
sudo nano /etc/postfix/main.cf

# Remove this line:
# transport_maps = sqlite:/opt/eu-email-webhook/data/transport.cf
```

#### Remove Amavis Listener from master.cf

```bash
# Edit master.cf and remove the Amavis section
sudo nano /etc/postfix/master.cf

# Remove these lines:
# 127.0.0.1:10025 inet n - - - - smtpd
#     -o content_filter=
#     -o local_recipient_maps=
#     -o relay_recipient_maps=
#     -o smtpd_restriction_classes=
#     -o smtpd_delay_reject=no
#     -o smtpd_client_restrictions=permit_mynetworks,reject
#     -o smtpd_helo_restrictions=
#     -o smtpd_sender_restrictions=
#     -o smtpd_recipient_restrictions=permit_mynetworks,reject
#     -o mynetworks=*********/8
#     -o smtpd_authorized_xforward_hosts=*********/8
```

### Step 4: Add spam-process-email Alias

```bash
# Add the spam-process-email alias
echo "spam-process-email: |/opt/eu-email-webhook/scripts/production/spam-process-email.js" | sudo tee -a /etc/aliases

# Rebuild aliases database
sudo newaliases

# Verify the alias was added
grep "spam-process-email" /etc/aliases
```

### Step 5: Update virtual_aliases.cf

```bash
# Update the virtual_aliases.cf to use direct table queries
sudo tee /opt/eu-email-webhook/data/virtual_aliases.cf > /dev/null <<'EOF'
dbpath = /opt/eu-email-webhook/data/postfix.db
query = SELECT destination FROM virtual_aliases WHERE email='%s' AND active=1
EOF
```

### Step 6: Update Database Routing

```bash
# Connect to SQLite database
sqlite3 /opt/eu-email-webhook/data/postfix.db

# Update existing aliases that were using Amavis routing
UPDATE virtual_aliases 
SET destination = 'process-email' 
WHERE destination LIKE '%amavis%' OR destination LIKE '%127.0.0.1:10024%';

# Verify the changes
SELECT email, destination FROM virtual_aliases LIMIT 10;

# Exit SQLite
.quit
```

### Step 7: Clean Up Old Configuration

```bash
# Remove transport configuration files
sudo rm -f /opt/eu-email-webhook/data/transport.cf
sudo rm -f /opt/eu-email-webhook/data/transport.db

# Remove any transport database references
sudo rm -f /etc/postfix/transport*
```

### Step 8: Reload Postfix

```bash
# Test Postfix configuration
sudo postfix check

# Reload Postfix configuration
sudo systemctl reload postfix

# Verify Postfix is running
sudo systemctl status postfix
```

### Step 9: Verify Migration

#### Check Services Status

```bash
# Postfix should be running
sudo systemctl status postfix

# SpamAssassin should be running
sudo systemctl status spamd

# Amavis should be stopped
sudo systemctl status amavis
```

#### Test Email Processing

```bash
# Check aliases
postmap -q "spam-process-email" /etc/aliases

# Test virtual alias lookup
postmap -q "@yourdomain.com" sqlite:/opt/eu-email-webhook/data/virtual_aliases.cf

# Check database content
sqlite3 /opt/eu-email-webhook/data/postfix.db "SELECT * FROM virtual_aliases WHERE active=1 LIMIT 5;"
```

#### Monitor Logs

```bash
# Monitor Postfix logs
sudo journalctl -u postfix.service -f

# Monitor application logs
cd /opt/eu-email-webhook
sudo docker compose -f docker-compose.prod.yml logs -f app

# Monitor SpamAssassin logs
sudo journalctl -u spamd.service -f
```

### Step 10: Optional Cleanup

If everything is working correctly, you can optionally remove Amavis completely:

```bash
# Remove Amavis package (optional - makes rollback harder)
sudo apt remove --purge amavisd-new -y
sudo apt autoremove -y

# Remove Amavis configuration directory
sudo rm -rf /etc/amavis
```

## Post-Migration Verification

### Test Email Flow

1. **Free User Test**: Send an email to a domain owned by a free user
   - Should be processed by `process-email` script
   - Should NOT have spam headers added

2. **Pro User Test**: Send an email to a domain owned by a pro user
   - Should be processed by `spam-process-email` script
   - Should have SpamAssassin headers added

### Check Plan Synchronization

The main application will automatically update domain routing when:
- Users upgrade/downgrade plans
- New domains are added
- Plan changes are processed

Monitor the application logs to ensure plan synchronization is working:

```bash
sudo docker compose -f /opt/eu-email-webhook/docker-compose.prod.yml logs -f app | grep -i "plan\|domain"
```

## Troubleshooting

### Common Issues

#### Emails Not Being Delivered

1. Check Postfix logs: `sudo journalctl -u postfix.service -f`
2. Verify virtual alias configuration: `postmap -q "@yourdomain.com" sqlite:/opt/eu-email-webhook/data/virtual_aliases.cf`
3. Check database connectivity: `sqlite3 /opt/eu-email-webhook/data/postfix.db ".tables"`

#### SpamAssassin Not Working for Pro Users

1. Check SpamAssassin service: `sudo systemctl status spamd`
2. Test spamc directly: `echo "test" | spamc -c`
3. Check spam-process-email script permissions: `ls -la /opt/eu-email-webhook/scripts/production/spam-process-email.js`

#### Plan Routing Not Updating

1. Check application logs for plan sync errors
2. Verify postfix-manager service is running: `sudo docker compose -f /opt/eu-email-webhook/docker-compose.prod.yml ps postfix-manager`
3. Test plan update API manually

### Rollback Procedure

If you need to rollback to the Amavis approach:

1. Restore backed up configuration files
2. Restart Amavis service
3. Reload Postfix configuration
4. Restore database from backup

```bash
# Restore configuration
sudo cp /etc/postfix/main.cf.backup.YYYYMMDD_HHMMSS /etc/postfix/main.cf
sudo cp /etc/postfix/master.cf.backup.YYYYMMDD_HHMMSS /etc/postfix/master.cf

# Restore database
sudo cp /opt/eu-email-webhook/data/postfix.db.backup.YYYYMMDD_HHMMSS /opt/eu-email-webhook/data/postfix.db

# Restart services
sudo systemctl start amavis
sudo systemctl enable amavis
sudo systemctl reload postfix
```

## Support

If you encounter issues during migration:

1. Check the troubleshooting section above
2. Review application and system logs
3. Ensure all prerequisites are met
4. Consider using the automated migration script instead

The new alias-based approach is more efficient and maintainable, providing better resource utilization and clearer separation between free and paid user email processing.
