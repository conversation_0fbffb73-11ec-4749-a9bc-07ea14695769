<template>
  <div class="card bg-base-100">
    <div class="card-body">
      <h2 class="card-title mb-6">
        <span class="badge badge-success bg-primary/10 text-base-content badge-sm badge-outline">Pro</span>
        Spam filtering
      </h2>
      
      <!-- Free Plan Limitations - Only show for free users -->
      <div v-if="!isProUser" class="bg-info/10 border border-info/20 rounded-lg p-4 mb-6">
        <h4 class="text-sm font-semibold text-info mb-2">Free plan limitations</h4>
        <div class="space-y-2 text-sm text-base-content/70">
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>No spam filtering available</span>
          </div>
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>All emails processed directly without spam analysis</span>
          </div>
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>No spam scores or reports in webhook payloads</span>
          </div>
        </div>
      </div>

      <!-- Success Message -->
      <div v-if="showSuccessMessage" class="bg-success/10 border border-success/20 rounded-lg p-3 mb-4">
        <div class="flex items-start gap-2">
          <svg class="w-4 h-4 text-success mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
          <div class="text-sm text-success">
            <strong>Spam filtering settings saved successfully!</strong>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="showErrorMessage" class="bg-error/10 border border-error/20 rounded-lg p-3 mb-4">
        <div class="flex items-start gap-2">
          <svg class="w-4 h-4 text-error mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div class="text-sm text-error">
            <strong>Error:</strong> {{ errorMessage }}
          </div>
        </div>
      </div>

      <!-- Spam Filtering Configuration -->
      <div class="bg-base-200/40 rounded-lg p-6">
        <h3 class="text-lg font-semibold mb-4 flex items-center gap-2">
          <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          SpamAssassin integration
        </h3>
        
        <div v-if="!isProUser" class="mb-6">
          <p class="text-sm text-base-content/70 mb-2">
            Upgrade to Pro to unlock advanced spam filtering powered by SpamAssassin
          </p>
          <div class="bg-base-300/50 rounded-lg p-4">
            <h4 class="text-sm font-semibold mb-2">Pro spam filtering includes:</h4>
            <ul class="text-sm text-base-content/70 space-y-1">
              <li>• Real-time spam analysis with SpamAssassin</li>
              <li>• Spam scores and detailed reports in webhook payloads</li>
              <li>• Configurable spam filtering per domain</li>
              <li>• Advanced spam detection rules and machine learning</li>
            </ul>
          </div>
        </div>
        
        <div v-else class="mb-6">
          <p class="text-sm text-base-content/70 mb-2">
            Configure spam filtering for your domains. When enabled, emails are processed through SpamAssassin 
            before being sent to your webhooks, providing spam scores and detailed analysis.
          </p>
        </div>

        <!-- Domain Spam Filtering Toggles -->
        <div v-if="domains.length > 0" class="space-y-4">
          <h4 class="text-sm font-semibold">Domain spam filtering</h4>
          
          <div class="space-y-3">
            <div 
              v-for="domain in domains" 
              :key="domain.id"
              class="flex items-center justify-between p-4 bg-base-100 rounded-lg border border-base-300"
              :class="{ 'opacity-60': !isProUser || loading }"
            >
              <div class="flex-1">
                <div class="font-medium">{{ domain.domain }}</div>
                <div class="text-sm text-base-content/60">
                  {{ domain.spamFiltering ? 'Emails processed through SpamAssassin' : 'Direct email processing (no spam filtering)' }}
                </div>
              </div>
              
              <div class="flex items-center space-x-3">
                <div class="form-control">
                  <label class="label cursor-pointer">
                    <input
                      type="checkbox"
                      :checked="domain.spamFiltering"
                      @change="toggleSpamFiltering(domain)"
                      :disabled="!isProUser || loading"
                      class="toggle toggle-primary"
                    />
                  </label>
                </div>
              </div>
            </div>
          </div>
          
          <div v-if="!isProUser" class="text-sm text-base-content/60 mt-4">
            <router-link to="/settings#billing" class="link">Upgrade to Pro</router-link> to enable spam filtering for your domains
          </div>
        </div>

        <!-- Spam Threshold Configuration -->
        <div v-if="isProUser && domains.length > 0 && domains.some(d => d.spamFiltering)" class="mt-8">
          <div class="bg-base-200/40 rounded-lg p-6">
            <h4 class="text-lg font-semibold mb-4 flex items-center gap-2">
              <svg class="w-5 h-5 text-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              Spam Detection Thresholds
            </h4>

            <div class="mb-6">
              <p class="text-sm text-base-content/70 mb-4">
                Configure spam detection sensitivity for your domains. Lower values are more strict (catch more spam),
                higher values are more permissive (allow more emails through).
              </p>

              <!-- Threshold Legend -->
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="bg-success/10 border border-success/20 rounded-lg p-3">
                  <div class="flex items-center gap-2 mb-1">
                    <div class="w-3 h-3 bg-success rounded-full"></div>
                    <span class="text-sm font-medium text-success">Green Zone</span>
                  </div>
                  <p class="text-xs text-base-content/60">Score below green threshold - emails pass through normally</p>
                </div>

                <div class="bg-warning/10 border border-warning/20 rounded-lg p-3">
                  <div class="flex items-center gap-2 mb-1">
                    <div class="w-3 h-3 bg-warning rounded-full"></div>
                    <span class="text-sm font-medium text-warning">Orange Zone</span>
                  </div>
                  <p class="text-xs text-base-content/60">Score between thresholds - marked as spam but delivered</p>
                </div>

                <div class="bg-error/10 border border-error/20 rounded-lg p-3">
                  <div class="flex items-center gap-2 mb-1">
                    <div class="w-3 h-3 bg-error rounded-full"></div>
                    <span class="text-sm font-medium text-error">Red Zone</span>
                  </div>
                  <p class="text-xs text-base-content/60">Score above red threshold - emails discarded entirely</p>
                </div>
              </div>
            </div>

            <!-- Per-Domain Threshold Controls -->
            <div class="space-y-6">
              <div
                v-for="domain in domains.filter(d => d.spamFiltering)"
                :key="`thresholds-${domain.id}`"
                class="bg-base-100 rounded-lg border border-base-300 p-4"
              >
                <h5 class="font-medium mb-4">{{ domain.domain }}</h5>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <!-- Green Threshold -->
                  <div>
                    <label class="label">
                      <span class="label-text flex items-center gap-2">
                        <div class="w-3 h-3 bg-success rounded-full"></div>
                        Green Threshold (Pass)
                      </span>
                      <span class="label-text-alt">{{ getThresholds(domain).green }}</span>
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="10"
                      step="0.1"
                      :value="getThresholds(domain).green"
                      @input="updateThreshold(domain, 'green', ($event.target as HTMLInputElement).value)"
                      :disabled="loading"
                      class="range range-success range-sm"
                    />
                    <div class="w-full flex justify-between text-xs px-2 text-base-content/50">
                      <span>0 (Strict)</span>
                      <span>10 (Permissive)</span>
                    </div>
                  </div>

                  <!-- Red Threshold -->
                  <div>
                    <label class="label">
                      <span class="label-text flex items-center gap-2">
                        <div class="w-3 h-3 bg-error rounded-full"></div>
                        Red Threshold (Discard)
                      </span>
                      <span class="label-text-alt">{{ getThresholds(domain).red }}</span>
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="20"
                      step="0.1"
                      :value="getThresholds(domain).red"
                      @input="updateThreshold(domain, 'red', ($event.target as HTMLInputElement).value)"
                      :disabled="loading"
                      class="range range-error range-sm"
                    />
                    <div class="w-full flex justify-between text-xs px-2 text-base-content/50">
                      <span>0 (Strict)</span>
                      <span>20 (Permissive)</span>
                    </div>
                  </div>
                </div>

                <div class="mt-4 text-xs text-base-content/60">
                  <strong>Current settings:</strong>
                  Emails scoring {{ getThresholds(domain).green }}+ marked as spam,
                  {{ getThresholds(domain).red }}+ discarded entirely
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- No Domains Message -->
        <div v-else class="text-center py-8">
          <svg class="w-12 h-12 text-base-content/30 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2M4 13h2m13-8l-4 4m0 0l-4-4m4 4V3" />
          </svg>
          <p class="text-base-content/60 mb-2">No domains configured</p>
          <p class="text-sm text-base-content/50">Add domains to configure spam filtering settings</p>
          <router-link to="/domains" class="btn btn-primary btn-sm mt-4">
            Add Domain
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useMetrics } from '../../composables/useMetrics'
import { useDomainApi } from '../../composables/useApi'
import type { Domain } from '../../types/domain'

// Composables
const { metricsData, loadMetrics } = useMetrics()
const { updateDomain } = useDomainApi()

// Local state for domains
const domains = ref<Domain[]>([])
const loading = ref(false)

// Local state
const showSuccessMessage = ref(false)
const showErrorMessage = ref(false)
const errorMessage = ref('')

// Check if user has Pro plan or higher
const isProUser = computed(() => {
  const planType = metricsData.value?.user?.planType || 'free'
  return planType === 'pro' || planType === 'enterprise'
})

// Load domains data
const loadDomains = async () => {
  try {
    loading.value = true
    const response = await fetch('/api/domains')
    const data = await response.json()
    domains.value = data.domains || []
  } catch (error) {
    console.error('Failed to load domains:', error)
    errorMessage.value = 'Failed to load domains. Please refresh the page.'
    showErrorMessage.value = true
  } finally {
    loading.value = false
  }
}

// Methods
const toggleSpamFiltering = async (domain: Domain) => {
  try {
    showSuccessMessage.value = false
    showErrorMessage.value = false

    const newSpamFiltering = !domain.spamFiltering

    await updateDomain(domain.id, {
      spamFiltering: newSpamFiltering
    })

    // Update local state
    domain.spamFiltering = newSpamFiltering

    // Show success message
    showSuccessMessage.value = true
    setTimeout(() => {
      showSuccessMessage.value = false
    }, 3000)
  } catch (err: any) {
    console.error('Failed to update spam filtering:', err)
    errorMessage.value = err.message || 'Failed to update spam filtering. Please try again.'
    showErrorMessage.value = true
    setTimeout(() => {
      showErrorMessage.value = false
    }, 5000)
  }
}

// Get spam thresholds for a domain (with defaults)
const getThresholds = (domain: Domain) => {
  // Extract thresholds from domain configuration or use defaults
  const config = domain.configuration as any || {}
  const spamConfig = config.spamFiltering || {}
  const thresholds = spamConfig.thresholds || {}

  return {
    green: thresholds.green || 2.0,
    red: thresholds.red || 5.0
  }
}

// Update spam threshold for a domain
const updateThreshold = async (domain: Domain, type: 'green' | 'red', value: string) => {
  try {
    loading.value = true

    const numValue = parseFloat(value)
    const currentThresholds = getThresholds(domain)

    // Validate thresholds (green must be less than red)
    let newThresholds = { ...currentThresholds }
    newThresholds[type] = numValue

    if (newThresholds.green >= newThresholds.red) {
      if (type === 'green') {
        newThresholds.red = newThresholds.green + 0.1
      } else {
        newThresholds.green = Math.max(0, newThresholds.red - 0.1)
      }
    }

    // Update domain configuration
    const updatedConfig = {
      ...((domain.configuration as any) || {}),
      spamFiltering: {
        ...((domain.configuration as any)?.spamFiltering || {}),
        enabled: domain.spamFiltering,
        thresholds: newThresholds
      }
    }

    await updateDomain(domain.id, {
      configuration: updatedConfig
    })

    // Update local domain state
    const domainIndex = domains.value.findIndex(d => d.id === domain.id)
    if (domainIndex !== -1) {
      domains.value[domainIndex] = {
        ...domains.value[domainIndex],
        configuration: updatedConfig
      }
    }

    // Show success message
    showSuccessMessage.value = true
    setTimeout(() => {
      showSuccessMessage.value = false
    }, 3000)

  } catch (err: any) {
    console.error('Failed to update spam thresholds:', err)
    errorMessage.value = err.message || 'Failed to update spam thresholds. Please try again.'
    showErrorMessage.value = true
    setTimeout(() => {
      showErrorMessage.value = false
    }, 5000)
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  // Load user metrics and domains
  await Promise.all([
    loadMetrics(),
    loadDomains()
  ])
})
</script>

<style scoped>
.toggle:checked {
  background-color: hsl(var(--p));
  border-color: hsl(var(--p));
}
</style>
