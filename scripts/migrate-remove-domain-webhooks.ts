#!/usr/bin/env tsx

/**
 * Migration script to remove domain webhooks and use only catch-all alias webhooks
 * 
 * This script:
 * 1. Ensures every domain has a catch-all alias
 * 2. Removes the webhook sync table
 * 3. Removes domain.webhookId column
 * 4. Updates Prisma schema
 * 
 * Usage:
 *   npx tsx scripts/migrate-remove-domain-webhooks.ts
 */

import { prisma } from '../src/backend/lib/prisma.js';

async function main() {
  console.log('🔄 Starting migration to remove domain webhooks...\n');

  try {
    // Step 1: Ensure every domain has a catch-all alias
    console.log('1. Ensuring every domain has a catch-all alias...');
    
    const domainsWithoutCatchAll = await prisma.domain.findMany({
      where: {
        aliases: {
          none: {
            email: {
              startsWith: '*@'
            }
          }
        }
      },
      include: {
        aliases: true,
        webhook: true
      }
    });

    if (domainsWithoutCatchAll.length > 0) {
      console.log(`Found ${domainsWithoutCatchAll.length} domains without catch-all aliases. Creating them...`);
      
      for (const domain of domainsWithoutCatchAll) {
        if (domain.webhook) {
          await prisma.alias.create({
            data: {
              domainId: domain.id,
              email: `*@${domain.domain}`,
              webhookId: domain.webhook.id,
              active: true
            }
          });
          console.log(`✅ Created catch-all alias for ${domain.domain}`);
        } else {
          console.warn(`⚠️  Domain ${domain.domain} has no webhook - skipping catch-all creation`);
        }
      }
    } else {
      console.log('✅ All domains already have catch-all aliases');
    }

    // Step 2: Drop webhook sync table
    console.log('\n2. Removing webhook sync table...');
    try {
      await prisma.$executeRaw`DROP TABLE IF EXISTS "webhook_syncs"`;
      console.log('✅ Webhook sync table removed');
    } catch (error) {
      console.log('ℹ️  Webhook sync table already removed or doesn\'t exist');
    }

    // Step 3: Remove domain webhook foreign key and column
    console.log('\n3. Removing domain webhook reference...');
    try {
      await prisma.$executeRaw`ALTER TABLE "domains" DROP CONSTRAINT IF EXISTS "domains_webhookId_fkey"`;
      console.log('✅ Removed foreign key constraint');
    } catch (error) {
      console.log('ℹ️  Foreign key constraint already removed');
    }

    try {
      await prisma.$executeRaw`ALTER TABLE "domains" DROP COLUMN IF EXISTS "webhookId"`;
      console.log('✅ Removed webhookId column from domains');
    } catch (error) {
      console.log('ℹ️  webhookId column already removed');
    }

    // Step 4: Clean up webhook references in Webhook model
    console.log('\n4. Migration completed successfully! 🎉');
    console.log('\nNext steps:');
    console.log('- Run: npx prisma generate');
    console.log('- Test domain webhook updates via UI');
    console.log('- Verify that catch-all alias webhooks are used for email processing');
    console.log('- Remove any remaining webhook sync service imports from your code');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
main().catch(console.error);
