import { prisma } from '../../lib/prisma.js';
import { PostfixManager } from '../postfix-manager.js';
import { DNSVerifier } from '../dns-verifier.js';
import { logger } from '../../utils/logger.js';
import { webSocketService } from '../websocket.service.js';

const postfixManager = new PostfixManager();
const dnsVerifier = new DNSVerifier();

export interface CreateDomainData {
  domain: string;
  webhookUrl?: string;
  webhookId?: string;
  active?: boolean;
  createCatchAll?: boolean;
  allowAttachments?: boolean;
  includeEnvelope?: boolean;
  userId: string;
}

export interface UpdateDomainData {
  active?: boolean;
  webhookId?: string;
  allowAttachments?: boolean;
  includeEnvelope?: boolean;
}

export class DomainService {
  /**
   * Get catch-all alias for a domain (used for webhook info)
   */
  private async getCatchAllAlias(domainId: string) {
    return await prisma.alias.findFirst({
      where: {
        domainId: domainId,
        email: { startsWith: '*@' }
      },
      include: { webhook: true }
    });
  }

  /**
   * Get all domains for a user
   */
  async getUserDomains(userId: string) {
    const dbDomains = await prisma.domain.findMany({
      where: { userId },
      include: {
        aliases: {
          include: { webhook: true },
          where: { active: true }
        }
      },
      orderBy: { createdAt: 'desc' },
    });

    // Make postfix call resilient to failures
    let postfixDomainsList: string[] = [];
    try {
      postfixDomainsList = await postfixManager.getConfiguredDomains();
    } catch (error: any) {
      logger.warn({ error: error.message }, 'Failed to get postfix domains, continuing without');
    }

    const domains = await Promise.all(dbDomains.map(async (d) => {
      // Get catch-all alias for webhook info
      const catchAllAlias = await this.getCatchAllAlias(d.id);
      
      const mappedDomain = {
        id: d.id,
        domain: d.domain,
        webhook: catchAllAlias?.webhook ? {
          id: catchAllAlias.webhook.id,
          name: catchAllAlias.webhook.name,
          url: catchAllAlias.webhook.url,
          verified: catchAllAlias.webhook.verified
        } : null,
        active: d.active,
        isVerified: d.verified,
        verificationStatus: d.verificationStatus,
        configuration: d.configuration,
        postfix_configured: postfixDomainsList.includes(d.domain),
        createdAt: d.createdAt.toISOString(),
        updatedAt: d.updatedAt.toISOString(),
        aliases: d.aliases.map(alias => ({
          id: alias.id,
          email: alias.email,
          webhookName: alias.webhook?.name,
          active: alias.active,
        })),
        lastVerificationAttempt: d.lastVerificationAttempt?.toISOString(),
        verificationFailureCount: d.verificationFailureCount,
        nextVerificationCheck: d.nextVerificationCheck?.toISOString(),
        expectedTxtRecord: d.domain ? DNSVerifier.getExpectedTXTRecord(d.domain) : null,
      };

      return mappedDomain;
    }));

    return {
      domains,
      total: domains.length,
      postfix_status: 'active',
      verified_count: domains.filter(d => d.isVerified).length,
      pending_verification: domains.filter(d => d.verificationStatus === 'PENDING').length,
    };
  }

  /**
   * Get domain by ID
   */
  async getDomain(domainId: string, userId: string) {
    const domainConfig = await prisma.domain.findFirst({ 
      where: { 
        id: domainId,
        userId  
      }, 
      include: { 
        aliases: { include: { webhook: true } }
      } 
    });

    if (!domainConfig) {
      return null;
    }

    // Get catch-all alias for webhook info
    const catchAllAlias = await this.getCatchAllAlias(domainId);
    
    // Make postfix call resilient to failures
    let postfixList: string[] = [];
    try {
      postfixList = await postfixManager.getConfiguredDomains();
    } catch (error: any) {
      logger.warn({ error: error.message }, 'Failed to get postfix domains, continuing without');
    }

    return {
      id: domainConfig.id,
      domain: domainConfig.domain,
      webhookUrl: catchAllAlias?.webhook?.url,
      webhookName: catchAllAlias?.webhook?.name,
      active: domainConfig.active,
      isVerified: domainConfig.verified,
      verificationStatus: domainConfig.verificationStatus,
      configuration: domainConfig.configuration,
      postfix_configured: postfixList.includes(domainConfig.domain),
      createdAt: domainConfig.createdAt.toISOString(),
      updatedAt: domainConfig.updatedAt.toISOString(),
      aliases: domainConfig.aliases.map((a: any) => ({
        id: a.id,
        email: a.email,
        webhookName: a.webhook?.name,
        active: a.active
      })),
      lastVerificationAttempt: domainConfig.lastVerificationAttempt?.toISOString(),
      verificationFailureCount: domainConfig.verificationFailureCount,
      nextVerificationCheck: domainConfig.nextVerificationCheck?.toISOString(),
      expectedTxtRecord: DNSVerifier.getExpectedTXTRecord(domainConfig.domain),
    };
  }

  /**
   * Create a new domain
   */
  async createDomain(data: CreateDomainData) {
    // Validate domain format
    if (!DNSVerifier.isValidDomain(data.domain)) {
      throw new Error('Invalid domain format');
    }

    if (!data.webhookUrl && !data.webhookId) {
      throw new Error('Either webhookUrl or webhookId is required');
    }

    const result = await prisma.$transaction(async (tx) => {
      // Get user
      const currentUser = await tx.user.findUnique({
        where: { id: data.userId }
      });
      
      if (!currentUser) {
        throw new Error('User not found');
      }
      
      // Get webhook for catch-all alias
      let webhook;

      if (data.webhookId) {
        // Use existing webhook by ID
        webhook = await tx.webhook.findFirst({
          where: { id: data.webhookId, userId: currentUser.id }
        });

        if (!webhook) {
          throw new Error('Webhook not found or does not belong to user');
        }
      } else if (data.webhookUrl) {
        // Legacy support: create or find webhook by URL
        webhook = await tx.webhook.findFirst({
          where: { url: data.webhookUrl, userId: currentUser.id }
        });

        if (!webhook) {
          webhook = await tx.webhook.create({
            data: {
              url: data.webhookUrl,
              name: `Default webhook for ${data.domain}`,
              description: `Auto-created webhook for ${data.domain}`,
              userId: currentUser.id
            }
          });
        }
      }

      // Prepare domain configuration
      const configuration: any = {};
      if (data.allowAttachments !== undefined) {
        configuration.allowAttachments = data.allowAttachments;
      }
      if (data.includeEnvelope !== undefined) {
        configuration.includeEnvelope = data.includeEnvelope;
      }

      // Create domain (without webhook reference)
      const newDomain = await tx.domain.create({
        data: {
          domain: data.domain,
          userId: currentUser.id,
          active: data.active ?? true,
          verified: false,
          verificationStatus: 'PENDING',
          nextVerificationCheck: new Date(Date.now() + 15 * 60 * 1000),
          configuration: Object.keys(configuration).length > 0 ? configuration : undefined,
        },
      });

      // Always create catch-all alias with the webhook
      const catchAllAlias = await tx.alias.create({
        data: {
          domainId: newDomain.id,
          email: `*@${newDomain.domain}`,
          webhookId: webhook!.id,
          active: true,
          configuration: Object.keys(configuration).length > 0 ? configuration : undefined,
        },
      });

      await postfixManager.addDomain(data.domain);

      return { domain: newDomain, webhook, catchAllAlias };
    });

    const instructions = {
      mx_record: {
        type: 'MX',
        name: '@',
        value: process.env.MAIL_HOSTNAME || 'your-mail-server.example.com',
        priority: 10
      },
      txt_record: {
        type: 'TXT',
        name: '@',
        value: DNSVerifier.getExpectedTXTRecord(result.domain.domain)
      },
      next_steps: [
        'Add the MX record to your DNS settings',
        'Add the TXT record for domain verification',
        'Wait for DNS propagation (up to 24 hours)',
        'Use the verify endpoint to check verification status'
      ]
    };

    const responseDomain = {
      id: result.domain.id,
      domain: result.domain.domain,
      apiKey: null,
      dkimPublicKey: null,
      dkimSelector: null,
      isVerified: result.domain.verified,
      verificationToken: null,
      createdAt: result.domain.createdAt.toISOString(),
      updatedAt: result.domain.updatedAt.toISOString(),
    };

    const webhookInfo = {
      id: result.webhook.id,
      url: result.webhook.url,
      name: result.webhook.name,
      verified: result.webhook.verified
    };

    return {
      success: true,
      domain: responseDomain,
      webhook: webhookInfo,
      instructions,
      postfix_status: 'configured'
    };
  }

  /**
   * Update a domain by ID
   */
  async updateDomainById(domainId: string, userId: string, updates: UpdateDomainData) {
    const existing = await prisma.domain.findFirst({
      where: { id: domainId, userId }
    });

    if (!existing) {
      throw new Error('Domain not found');
    }

    const dataToUpdate: any = { updatedAt: new Date() };
    if (updates.active !== undefined) dataToUpdate.active = updates.active;

    // Handle webhook updates by updating the catch-all alias
    if (updates.webhookId !== undefined) {
      // Verify webhook belongs to user
      const webhook = await prisma.webhook.findFirst({
        where: { id: updates.webhookId, userId }
      });
      if (!webhook) {
        throw new Error('Webhook not found');
      }

      // Update catch-all alias webhook instead of domain webhook
      const catchAllAlias = await this.getCatchAllAlias(domainId);
      if (catchAllAlias) {
        await prisma.alias.update({
          where: { id: catchAllAlias.id },
          data: { webhookId: updates.webhookId }
        });
      }
    }

    // Handle configuration updates
    if (updates.allowAttachments !== undefined || updates.includeEnvelope !== undefined) {
      const currentConfig = existing.configuration as any || {};
      const newConfig = {
        ...currentConfig,
        ...(updates.allowAttachments !== undefined && { allowAttachments: updates.allowAttachments }),
        ...(updates.includeEnvelope !== undefined && { includeEnvelope: updates.includeEnvelope })
      };
      dataToUpdate.configuration = newConfig;
    }

    const updated = await prisma.domain.update({
      where: { id: domainId },
      data: dataToUpdate
    });

    const responseDomain = {
      id: updated.id,
      domain: updated.domain,
      apiKey: null,
      dkimPublicKey: null,
      dkimSelector: null,
      isVerified: updated.verified,
      verificationToken: null,
      createdAt: updated.createdAt.toISOString(),
      updatedAt: updated.updatedAt.toISOString()
    };

    return {
      success: true,
      domain: responseDomain,
      message: 'Domain updated successfully'
    };
  }

  /**
   * Update domain status by ID
   */
  async updateDomainStatus(domainId: string, userId: string, active: boolean) {
    // Verify domain belongs to user
    const existing = await prisma.domain.findFirst({
      where: {
        id: domainId,
        userId
      }
    });

    if (!existing) {
      throw new Error('Domain not found');
    }

    // Update the domain status
    const updated = await prisma.domain.update({
      where: { id: domainId },
      data: {
        active: active,
        updatedAt: new Date()
      }
    });

    return {
      success: true,
      domain: {
        id: updated.id,
        domain: updated.domain,
        active: updated.active,
        updatedAt: updated.updatedAt.toISOString()
      },
      message: `Domain ${updated.domain} ${active ? 'activated' : 'deactivated'} successfully`
    };
  }

  /**
   * Update domain webhook by ID (actually updates catch-all alias webhook)
   */
  async updateDomainWebhook(domainId: string, userId: string, webhookId: string) {
    // Verify domain belongs to user
    const existingDomain = await prisma.domain.findFirst({
      where: {
        id: domainId,
        userId
      }
    });

    if (!existingDomain) {
      throw new Error('Domain not found');
    }

    // Verify webhook belongs to user
    const webhook = await prisma.webhook.findFirst({
      where: {
        id: webhookId,
        userId
      }
    });

    if (!webhook) {
      throw new Error('Webhook not found');
    }

    // Update the catch-all alias webhook instead of domain webhook
    const catchAllAlias = await this.getCatchAllAlias(domainId);
    if (!catchAllAlias) {
      throw new Error('No catch-all alias found for domain');
    }

    await prisma.alias.update({
      where: { id: catchAllAlias.id },
      data: { webhookId: webhookId }
    });

    return {
      success: true,
      domain: {
        id: existingDomain.id,
        domain: existingDomain.domain,
        webhookId: webhookId,
        updatedAt: new Date().toISOString()
      },
      webhook: {
        id: webhook.id,
        name: webhook.name,
        url: webhook.url
      },
      message: `Domain webhook updated to "${webhook.name}"`
    };
  }

  /**
   * Delete a domain by ID
   */
  async deleteDomain(domainId: string, userId: string) {
    // Verify domain belongs to user
    const existingDomain = await prisma.domain.findFirst({
      where: {
        id: domainId,
        userId
      }
    });

    if (!existingDomain) {
      throw new Error('Domain not found');
    }

    // Delete the domain (aliases will be cascade deleted)
    await prisma.domain.delete({
      where: { id: domainId }
    });

    // Remove from postfix configuration
    try {
      await postfixManager.removeDomain(existingDomain.domain);
    } catch (error) {
      console.warn(`Failed to remove domain ${existingDomain.domain} from postfix:`, error);
      // Don't fail the operation if postfix removal fails
    }

    return {
      success: true,
      message: `Domain ${existingDomain.domain} deleted successfully`
    };
  }

  /**
   * Verify a domain by ID
   */
  async verifyDomain(domainId: string, userId: string) {
    // Verify domain belongs to user
    const existingDomain = await prisma.domain.findFirst({
      where: {
        id: domainId,
        userId
      }
    });

    if (!existingDomain) {
      throw new Error('Domain not found');
    }

    // Clear DNS cache for this domain to ensure fresh verification attempt
    dnsVerifier.clearCache(existingDomain.domain);
    logger.info({ domain: existingDomain.domain }, 'Manual verification requested - DNS cache cleared');

    // Perform DNS verification
    const verificationResult = await dnsVerifier.verifyDomainOwnership(existingDomain.domain);

    // Calculate verification status using same logic as verification worker
    const now = new Date();
    let nextCheck: Date | null;
    let newStatus: 'PENDING' | 'VERIFIED' | 'FAILED';
    const newFailureCount = verificationResult.verified ? 0 : existingDomain.verificationFailureCount + 1;

    if (verificationResult.verified) {
      // Success - no need for next check
      nextCheck = null;
      newStatus = 'VERIFIED';
    } else {
      // Failed - use same logic as verification worker
      if (newFailureCount >= 10) {
        // Too many failures - mark as failed and check daily
        nextCheck = new Date(now.getTime() + 24 * 60 * 60 * 1000);
        newStatus = 'FAILED';
      } else {
        // Keep as pending and retry in 15 minutes
        nextCheck = new Date(now.getTime() + 15 * 60 * 1000);
        newStatus = 'PENDING';
      }
    }

    // Update domain verification status
    const updated = await prisma.domain.update({
      where: { id: domainId },
      data: {
        verified: verificationResult.verified,
        verificationStatus: newStatus,
        lastVerificationAttempt: now,
        verificationFailureCount: newFailureCount,
        nextVerificationCheck: nextCheck,
        updatedAt: now
      }
    });

    // Emit WebSocket event for real-time updates
    webSocketService.emitDomainVerificationUpdated(userId, {
      domainId: updated.id,
      domain: updated.domain,
      verified: updated.verified,
      verificationStatus: updated.verificationStatus,
      verificationFailureCount: newFailureCount,
      nextVerificationCheck: nextCheck?.toISOString(),
      error: verificationResult.error,
      timestamp: new Date().toISOString(),
    });

    return {
      success: true,
      domain: {
        id: updated.id,
        domain: updated.domain,
        verified: updated.verified,
        verificationStatus: updated.verificationStatus,
        updatedAt: updated.updatedAt.toISOString()
      },
      verification: verificationResult,
      message: verificationResult.verified ? 'Domain verified successfully' : 'Domain verification failed'
    };
  }

  /**
   * Get spam filtering settings for a domain
   */
  async getSpamFilterSettings(domainId: string, userId: string) {
    const domain = await prisma.domain.findFirst({
      where: { id: domainId, userId }
    });

    if (!domain) {
      throw new Error('Domain not found');
    }

    // Get current settings from domain configuration
    const config = domain.configuration as any || {};
    const spamConfig = config.spamFiltering || {};

    return {
      enabled: spamConfig.enabled || false,
      thresholds: {
        green: spamConfig.thresholds?.green || 2.0,
        red: spamConfig.thresholds?.red || 5.0
      }
    };
  }

  /**
   * Update spam filtering settings for a domain
   */
  async updateSpamFilterSettings(
    domainId: string,
    userId: string,
    settings: { enabled: boolean; thresholds: { green: number; red: number } }
  ) {
    const domain = await prisma.domain.findFirst({
      where: { id: domainId, userId }
    });

    if (!domain) {
      throw new Error('Domain not found');
    }

    // Update domain configuration
    const currentConfig = domain.configuration as any || {};
    const updatedConfig = {
      ...currentConfig,
      spamFiltering: {
        enabled: settings.enabled,
        thresholds: settings.thresholds
      }
    };

    // Update database
    const updated = await prisma.domain.update({
      where: { id: domainId },
      data: { configuration: updatedConfig }
    });

    // Update Postfix configuration via postfix-manager service
    try {
      await postfixManager.updateDomainSpamFiltering(domain.domain, settings.enabled);
      logger.info({
        domain: domain.domain,
        enabled: settings.enabled,
        thresholds: settings.thresholds
      }, 'Updated domain spam filtering settings');
    } catch (error) {
      logger.error({
        domain: domain.domain,
        error: error instanceof Error ? error.message : error
      }, 'Failed to update Postfix spam filtering configuration');
      // Don't throw here - the database update succeeded
    }

    return {
      success: true,
      message: 'Spam filtering settings updated successfully',
      settings: {
        enabled: settings.enabled,
        thresholds: settings.thresholds
      }
    };
  }
}
