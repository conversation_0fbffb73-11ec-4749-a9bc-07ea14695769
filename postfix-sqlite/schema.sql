-- ========================================
-- Table: virtual_domains
-- ========================================
CREATE TABLE IF NOT EXISTS virtual_domains (
    domain VARCHAR(255) PRIMARY KEY,
    destination VARCHAR(255) NOT NULL DEFAULT 'process-email',
    active BOOLEAN DEFAULT 1,
    spam_filtering BOOLEAN DEFAULT 0,                        -- Enable/disable spam filtering
    spam_threshold_green REAL DEFAULT 2.0,                   -- Below: pass through
    spam_threshold_red REAL DEFAULT 5.0,                     -- Above: marked as spam
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance on virtual_domains
CREATE INDEX IF NOT EXISTS idx_virtual_domains_active 
    ON virtual_domains(domain, active);

CREATE INDEX IF NOT EXISTS idx_virtual_domains_spam_filtering 
    ON virtual_domains(domain, spam_filtering, active);

-- ========================================
-- Table: virtual_aliases
-- ========================================
CREATE TABLE IF NOT EXISTS virtual_aliases (
    email VARCHAR(255) PRIMARY KEY,
    destination VARCHAR(255) NOT NULL,
    domain VARCHAR(255),
    active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (domain) REFERENCES virtual_domains(domain) ON DELETE CASCADE
);

-- Indexes for performance on virtual_aliases
CREATE INDEX IF NOT EXISTS idx_virtual_aliases_active 
    ON virtual_aliases(email, active);

CREATE INDEX IF NOT EXISTS idx_virtual_aliases_domain 
    ON virtual_aliases(domain);

-- ========================================
-- Triggers
-- ========================================
-- Automatically update 'updated_at' on domain changes
DROP TRIGGER IF EXISTS update_virtual_domains_timestamp;
CREATE TRIGGER IF NOT EXISTS update_virtual_domains_timestamp 
    AFTER UPDATE ON virtual_domains
BEGIN
    UPDATE virtual_domains 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE domain = NEW.domain;
END;

-- Automatically update 'updated_at' on alias changes
CREATE TRIGGER IF NOT EXISTS update_virtual_aliases_timestamp 
    AFTER UPDATE ON virtual_aliases
BEGIN
    UPDATE virtual_aliases 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE email = NEW.email;
END;
