<template>
  <div class="px-6 py-8 mx-auto max-w-7xl lg:px-8">
    <div class="mb-8">
      <h1 class="text-2xl font-bold">Settings</h1>
      <p class="mt-2">Manage your account settings and preferences.</p>
    </div>

    <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
      <!-- Settings Navigation -->
      <div class="lg:col-span-1">
        <nav class="space-y-1">
          <a
            href="#profile"
            @click.prevent="setActiveSection('profile')"
            :class="[
              'flex items-center px-3 py-2 text-sm font-medium rounded-l-md',
              activeSection === 'profile'
                ? 'text-primary border-r-2 border-primary bg-primary/10'
                : 'text-base-content/70 hover:text-base-content hover:bg-base-200'
            ]"
          >
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            Profile
          </a>
          <a
            href="#api-keys"
            @click.prevent="setActiveSection('api-keys')"
            :class="[
              'flex items-center px-3 py-2 text-sm font-medium rounded-l-md',
              activeSection === 'api-keys'
                ? 'text-primary border-r-2 border-primary bg-primary/10'
                : 'text-base-content/70 hover:text-base-content hover:bg-base-200'
            ]"
          >
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10zM12 11v2m0 4h.01" />
            </svg>
            API keys
          </a>
          <a
            href="#storage"
            @click.prevent="setActiveSection('storage')"
            :class="[
              'flex items-center px-3 py-2 text-sm font-medium rounded-l-md',
              activeSection === 'storage'
                ? 'text-primary border-r-2 border-primary bg-primary/10'
                : 'text-base-content/70 hover:text-base-content hover:bg-base-200'
            ]"
          >
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M5 19a2 2 0 01-2-2V7a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1M5 19h14a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 01-2 2z" />
            </svg>
            Storage
          </a>
          <a
            href="#spam"
            @click.prevent="setActiveSection('spam')"
            :class="[
              'flex items-center px-3 py-2 text-sm font-medium rounded-l-md',
              activeSection === 'spam'
                ? 'text-primary border-r-2 border-primary bg-primary/10'
                : 'text-base-content/70 hover:text-base-content hover:bg-base-200'
            ]"
          >
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Spam filtering
          </a>
          <a
            href="#billing"
            @click.prevent="setActiveSection('billing')"
            :class="[
              'flex items-center px-3 py-2 text-sm font-medium rounded-l-md',
              activeSection === 'billing'
                ? 'text-primary border-r-2 border-primary bg-primary/10'
                : 'text-base-content/70 hover:text-base-content hover:bg-base-200'
            ]"
          >
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
            </svg>
            Billing
          </a>
        </nav>
      </div>

      <!-- Settings Content -->
      <div class="lg:col-span-2">

        <!-- Profile Section -->
        <div v-if="activeSection === 'profile'" class="card bg-base-100">
          <div class="card-body">
            <h2 class="card-title mb-6">Profile information</h2>

            <div class="bg-base-200/40 rounded-lg p-6">
              <div class="space-y-4">
                <div>
                  <label class="label">
                    <span class="label-text">Display name</span>
                  </label>
                  <input
                    type="text"
                    v-model="displayName"
                    placeholder="Enter your display name"
                    class="input input-bordered w-full"
                  >
                </div>

                <div>
                  <label class="label">
                    <span class="label-text">Email address</span>
                  </label>
                  <div class="flex gap-2">
                    <input
                      type="email"
                      :value="currentUser?.email || ''"
                      disabled
                      class="input input-bordered w-full cursor-not-allowed bg-base-300"
                    >
                    <button
                      type="button"
                      @click="showEmailForm = !showEmailForm"
                      class="btn btn-outline btn-sm"
                    >
                      Change email
                    </button>
                  </div>
                  <label class="label">
                    <span class="label-text-alt text-xs">Click "Change" to update your email address.</span>
                  </label>
                </div>

                <!-- Email Change Form -->
                <div v-if="showEmailForm" class="mt-4 p-4 border border-base-300 rounded-lg bg-base-100">
                  <h4 class="font-medium mb-3">Change email address</h4>
                  <div class="space-y-3">
                    <div>
                      <label class="label">
                        <span class="label-text">New email address</span>
                      </label>
                      <input
                        type="email"
                        v-model="newEmail"
                        placeholder="Enter new email address"
                        class="input input-bordered w-full"
                      >
                    </div>
                    <div>
                      <label class="label">
                        <span class="label-text">Current password</span>
                      </label>
                      <input
                        type="password"
                        v-model="emailChangePassword"
                        placeholder="Enter your current password"
                        class="input input-bordered w-full"
                      >
                    </div>
                    <div class="flex gap-2">
                      <button
                        type="button"
                        @click="changeEmail"
                        :disabled="changingEmail"
                        class="btn btn-primary"
                      >
                        {{ changingEmail ? 'Changing...' : 'Change email' }}
                      </button>
                      <button
                        type="button"
                        @click="showEmailForm = false; emailChangePassword = ''"
                        class="btn btn-ghost"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                </div>

                <!-- Password Section -->
                <div class="mt-6">
                  <div class="flex items-center justify-between">
                    <div>
                      <label class="label">
                        <span class="label-text">Password</span>
                      </label>
                      <p class="text-sm text-base-content/70">••••••••••••</p>
                    </div>
                    <button
                      type="button"
                      @click="showPasswordForm = !showPasswordForm"
                      class="btn btn-outline btn-sm"
                    >
                      Change password
                    </button>
                  </div>
                </div>

                <!-- Password Change Form -->
                <div v-if="showPasswordForm" class="mt-4 p-4 border border-base-300 rounded-lg bg-base-100">
                  <h4 class="font-medium mb-3">Change password</h4>
                  <div class="space-y-3">
                    <div>
                      <label class="label">
                        <span class="label-text">Current password</span>
                      </label>
                      <input
                        type="password"
                        v-model="currentPassword"
                        placeholder="Enter your current password"
                        class="input input-bordered w-full"
                      >
                    </div>
                    <div>
                      <label class="label">
                        <span class="label-text">New password</span>
                      </label>
                      <input
                        type="password"
                        v-model="newPassword"
                        placeholder="Enter new password (min 6 characters)"
                        class="input input-bordered w-full"
                      >
                    </div>
                    <div>
                      <label class="label">
                        <span class="label-text">Confirm new password</span>
                      </label>
                      <input
                        type="password"
                        v-model="confirmPassword"
                        placeholder="Confirm new password"
                        class="input input-bordered w-full"
                      >
                    </div>
                    <div class="flex gap-2">
                      <button
                        type="button"
                        @click="changePassword"
                        :disabled="changingPassword"
                        class="btn btn-primary"
                      >
                        {{ changingPassword ? 'Changing...' : 'Change password' }}
                      </button>
                      <button
                        type="button"
                        @click="showPasswordForm = false; currentPassword = ''; newPassword = ''; confirmPassword = ''"
                        class="btn btn-ghost"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                </div>

                <div class="pt-4">
                  <button
                    type="button"
                    @click="saveChanges"
                    :disabled="saving"
                    class="btn btn-primary"
                  >
                    {{ saving ? 'Saving...' : 'Save changes' }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- API Keys Section -->
        <ApiKeysSection v-if="activeSection === 'api-keys'" />

        <!-- Spam Filtering Section -->
        <SpamSection v-if="activeSection === 'spam'" />

        <!-- Billing Section -->
        <BillingSection v-if="activeSection === 'billing'" />

        <!-- Storage Section -->
        <StorageSection v-if="activeSection === 'storage'" />
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import BillingSection from './BillingSection.vue'
import ApiKeysSection from './ApiKeysSection.vue'
import SpamSection from './SpamSection.vue'
import StorageSection from './StorageSection.vue'
import { useMetrics } from '../../composables/useMetrics'

interface User {
  id: string
  email: string
  name?: string
  planType: string
  verified: boolean
}

// Router
const route = useRoute()
const router = useRouter()

// State
const activeSection = ref('profile')
const user = ref<User | null>(null)
const displayName = ref('')
const currentPassword = ref('')
const newPassword = ref('')
const confirmPassword = ref('')
const newEmail = ref('')
const emailChangePassword = ref('')
const saving = ref(false)
const changingPassword = ref(false)
const changingEmail = ref(false)
const showPasswordForm = ref(false)
const showEmailForm = ref(false)

// Use metrics to get user data
const { metricsData, loadMetrics } = useMetrics()

// Computed
const currentUser = computed(() => {
  return user.value || metricsData.value?.user || null
})

// Navigation methods
const setActiveSection = (section: string) => {
  activeSection.value = section
  // Update URL hash without triggering navigation
  router.replace({ hash: `#${section}` })
}

const initializeFromHash = () => {
  const hash = route.hash.replace('#', '')
  const validSections = ['profile', 'api-keys', 'storage', 'billing']

  if (hash && validSections.includes(hash)) {
    activeSection.value = hash
  } else {
    activeSection.value = 'profile'
  }
}

// Methods
const loadUserProfile = async () => {
  try {
    await loadMetrics()
    if (metricsData.value?.user) {
      user.value = metricsData.value.user
      displayName.value = metricsData.value.user.name || ''
      newEmail.value = metricsData.value.user.email
    }
  } catch (error) {
    console.error('Failed to load user profile:', error)
  }
}

const saveChanges = async () => {
  saving.value = true
  try {
    const response = await fetch('/api/profile', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify({
        name: displayName.value || null
      })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || 'Failed to update profile')
    }

    const data = await response.json()
    if (data.success && data.user) {
      user.value = { ...user.value, ...data.user }
      alert('Profile updated successfully!')
    }
  } catch (error: any) {
    console.error('Failed to save changes:', error)
    alert(error.message || 'Failed to save changes. Please try again.')
  } finally {
    saving.value = false
  }
}

const changePassword = async () => {
  if (newPassword.value !== confirmPassword.value) {
    alert('New passwords do not match')
    return
  }

  if (newPassword.value.length < 6) {
    alert('New password must be at least 6 characters long')
    return
  }

  changingPassword.value = true
  try {
    const response = await fetch('/api/profile/password', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify({
        currentPassword: currentPassword.value,
        newPassword: newPassword.value
      })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || 'Failed to change password')
    }

    // Clear form and hide
    currentPassword.value = ''
    newPassword.value = ''
    confirmPassword.value = ''
    showPasswordForm.value = false
    alert('Password changed successfully!')
  } catch (error: any) {
    console.error('Failed to change password:', error)
    alert(error.message || 'Failed to change password. Please try again.')
  } finally {
    changingPassword.value = false
  }
}

const changeEmail = async () => {
  if (!newEmail.value || !emailChangePassword.value) {
    alert('Please fill in all fields')
    return
  }

  changingEmail.value = true
  try {
    const response = await fetch('/api/profile/email', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify({
        newEmail: newEmail.value,
        password: emailChangePassword.value
      })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || 'Failed to change email')
    }

    const data = await response.json()
    if (data.success && data.user) {
      user.value = { ...user.value, ...data.user }
      emailChangePassword.value = ''
      showEmailForm.value = false
      alert('Email address changed successfully!')
    }
  } catch (error: any) {
    console.error('Failed to change email:', error)
    alert(error.message || 'Failed to change email. Please try again.')
  } finally {
    changingEmail.value = false
  }
}

// Watch for route hash changes
watch(() => route.hash, () => {
  initializeFromHash()
}, { immediate: false })

watch(currentUser, (newUser) => {
  if (newUser) {
    displayName.value = newUser.name || ''
  }
}, { immediate: true })

onMounted(() => {
  // Initialize section from URL hash first
  initializeFromHash()
  // Then load user profile
  loadUserProfile()
})
</script>

<style scoped>
/* Settings page specific styles */
</style>
