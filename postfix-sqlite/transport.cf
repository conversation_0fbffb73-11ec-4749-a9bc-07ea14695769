# ⚠️  OBSOLETE FILE - DO NOT USE ⚠️
#
# This file was used for the old Amavis transport-based spam filtering approach.
# As of 2025-06-27, the system has migrated to alias-based spam filtering.
#
# OLD APPROACH (Amavis Transport - OBSOLETE):
# - Used transport maps to route emails through Amavis (127.0.0.1:10024)
# - Required complex Amavis configuration and integration
# - Single point of failure and resource intensive
#
# NEW APPROACH (Alias-Based - CURRENT):
# - Uses virtual_alias_maps with dynamic SQL queries
# - Routes emails to different processing scripts based on spam_filtering column
# - Free users: <EMAIL> (direct processing)
# - Pro+ users: <EMAIL> (SpamAssassin + processing)
# - Simpler, more reliable, and resource efficient
#
# MIGRATION STATUS: This file should be removed from production systems
# The new system uses virtual_domains.cf and virtual_aliases.cf instead

# OLD QUERY (DO NOT USE):
# query = SELECT CASE WHEN spam_filtering = 1 THEN 'amavis:[127.0.0.1]:10024' ELSE NULL END FROM virtual_domains WHERE domain='%s' AND active=1

# Database connection settings
dbpath = /opt/eu-email-webhook/data/postfix.db

# Optional: Connection settings for better performance
# result_format = %s
# expansion_limit = 0

# Security settings
# domain = 
# hosts = localhost
# user = 
# password = 

# Debugging (set to yes for troubleshooting)
# debuglevel = 0
